using System;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using GoTrack.Addresses;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using GoTrack.Identity;
using GoTrack.Mobile.MobileIdentityUser;
using GoTrack.Msisdns;
using GoTrack.Notifications.NotificationFactories.UserWelcomeNotifications;
using GoTrack.Requests;
using GoTrack.Totps;
using GoTrack.TrackAccounts;
using GoTrack.Users;
using Volo.Abp;
using Volo.Abp.Data;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Identity;
using Volo.Abp.MultiTenancy;
using Volo.Abp.Users;
using GoTrack.UserTrackAccountAssociations;
using Volo.Abp.Content;
using Volo.Abp.EventBus.Distributed;
using System.Globalization;
using GoTrack.Mobile.MobileIdentityUsers.DTOs;
using Microsoft.Extensions.DependencyInjection;
using Volo.Abp.Domain.Entities;


namespace GoTrack.Mobile.MobileIdentityUsers;

public class MobileIdentityUserAppService : GoTrackMobileAppService, IMobileIdentityUserAppService
{
    private readonly IRepository<UserTrackAccountAssociation, Guid> _userTrackAccountAssociationRepository;
    private readonly IRepository<IdentityUserProfile, Guid> _identityUserProfileRepository;
    private readonly IRepository<TrackAccount, Guid> _trackAccountRepository;
    private readonly IRepository<Request, Guid> _requestRepository;
    private readonly IDistributedEventBus _distributedEventBus;
    private readonly IIdentityUserRepository _userRepository;
    private readonly IdentityUserManager _userManager;
    private readonly IAddressManager _addressManager;
    private readonly MsisdnManager _msisdnManager;
    private readonly ITotpManager _totpManager;
    private readonly IDataFilter _dataFilter;
    private readonly IdentityUserProfileManager _identityUserProfileManager;

    private GoTrackIdentityUserManager GoTrackIdentityUserManager
        => LazyServiceProvider.GetRequiredService<GoTrackIdentityUserManager>();
    private IdentityUserManager IdentityUserManager
        => LazyServiceProvider.GetRequiredService<IdentityUserManager>();

    public MobileIdentityUserAppService(IRepository<IdentityUserProfile, Guid> identityUserProfileRepository,
        IIdentityUserRepository userRepository, IdentityUserManager userManager, IAddressManager addressManager,
        MsisdnManager msisdnManager, IDataFilter dataFilter, ITotpManager totpManager,
        IDistributedEventBus distributedEventBus, IRepository<TrackAccount, Guid> trackAccountRepository,
        IRepository<Request, Guid> requestRepository,
        IRepository<UserTrackAccountAssociation, Guid> userTrackAccountAssociationRepository,
        IdentityUserProfileManager identityUserProfileManager)
    {
        _userTrackAccountAssociationRepository = userTrackAccountAssociationRepository;
        _identityUserProfileRepository = identityUserProfileRepository;
        _trackAccountRepository = trackAccountRepository;
        _requestRepository = requestRepository;
        _distributedEventBus = distributedEventBus;
        _userRepository = userRepository;
        _userManager = userManager;
        _addressManager = addressManager;
        _msisdnManager = msisdnManager;
        _totpManager = totpManager;
        _dataFilter = dataFilter;
        _identityUserProfileManager = identityUserProfileManager;
    }

    [RemoteService(false)]
    public async Task<string> CreateIfNotExistingAsync(MobileUserSignInDto dto)
    {
        var newUserMsisdn = _msisdnManager.Create(dto.Msisdn);

        _totpManager.ValidateCode(newUserMsisdn, dto.Otp);

        IdentityUser? customerUser;
        using (_dataFilter.Disable<IMultiTenant>())
        {
            customerUser = await _userRepository.FindByNormalizedUserNameAsync(newUserMsisdn.ToString());
        }

        if (customerUser is not null)
            return customerUser.NormalizedUserName;

        customerUser = new IdentityUser(GuidGenerator.Create(), newUserMsisdn.ToString(),
            newUserMsisdn + UsersConstants.UserEmptyEmailSuffix); // todo find way to make Email null 
        customerUser.SetPhoneNumber(newUserMsisdn.ToString(), true);
        customerUser.SetSubClass(UserSubClass.Customer);

        customerUser.AddClaim(GuidGenerator, new Claim(UsersConstants.ProfileRequiresUpdateClaimName, "True"));

        var result = await _userManager.CreateAsync(customerUser);
        result.CheckErrors();

        var query = await _userTrackAccountAssociationRepository.GetQueryableAsync();

        query = query.Where(x => x.PhoneNumber == newUserMsisdn);

        var userTrackAccountAssociation = await AsyncExecuter.FirstOrDefaultAsync(query);
        if (userTrackAccountAssociation is not null)
        {
            userTrackAccountAssociation.SetUserId(customerUser.Id);
            await _userTrackAccountAssociationRepository.UpdateAsync(userTrackAccountAssociation);
        }

        var userWelcomeNotificationFactory =
            LazyServiceProvider.LazyGetRequiredService<UserWelcomeNotificationFactory>();

        var eto = await userWelcomeNotificationFactory.CreateAsync(new UserWelcomeNotificationDataModel(),
            userId: customerUser.Id);

        await _distributedEventBus.PublishAsync(eto);
        
        return customerUser.NormalizedUserName;
    }

    [Authorize]
    public async Task UpdateProfileAsync(MobileUserUpdateProfileDto profileDto)
    {
        var user = await _userRepository.FindAsync((Guid)CurrentUser?.Id!);

        if (user is null)
        {
            throw new EntityNotFoundException(typeof(IdentityUser), CurrentUser.Id);
        }
        
        user.ReplaceClaim(new Claim(UsersConstants.ProfileRequiresUpdateClaimName, "True"),
            new Claim(UsersConstants.ProfileRequiresUpdateClaimName, "False"));

        user.Name = profileDto.FirstName;
        user.Surname = profileDto.LastName;

        (await _userManager.UpdateAsync(user)).CheckErrors();
        (await _userManager.UpdateSecurityStampAsync(user)).CheckErrors();

        var userProfile = await _identityUserProfileRepository.AnyAsync(p => p.Id == (Guid)CurrentUser.Id!);

        Address? address = null;
        
        if (profileDto.Address is not null)
        {
            address = await _addressManager.CreateAsync(profileDto.Address.Street, profileDto.Address.Area,
                profileDto.Address.City, profileDto.Address.Governorate, profileDto.Address.Country);
        }
       
        if (userProfile)
        {
            await _identityUserProfileManager.UpdateAsync((Guid)CurrentUser.Id!, address);
            await _identityUserProfileManager.UpdateEmailAsync(user!.Id, profileDto.Email, CultureInfo.CurrentCulture.Name);
        }
        else
        {
            await _identityUserProfileManager.CreateAsync(user, address);
            await _identityUserProfileManager.CreateEmailAsync(user, profileDto.Email, CultureInfo.CurrentCulture.Name);
        }
    }

    [Authorize]
    public async Task<GetMobileUserProfileDto> GetProfileAsync()
    {
        var userProfile = await _identityUserProfileRepository.FindAsync(profile => profile.Id == CurrentUser.Id);

        GetMobileUserProfileDto getMobileUserProfileDto;

        if (userProfile is not null)
        {
            getMobileUserProfileDto = ObjectMapper.Map<IdentityUserProfile, GetMobileUserProfileDto>(userProfile);
        }
        else
        {
            var user = await _userRepository.GetAsync((Guid)CurrentUser.Id!);
            getMobileUserProfileDto = ObjectMapper.Map<IdentityUser, GetMobileUserProfileDto>(user);
        }

        getMobileUserProfileDto.TrackAccountCount = (uint)await _trackAccountRepository.CountAsync(account =>
            account.UserTrackAccountAssociations.Any(association => association.UserId == CurrentUser.GetId()));
        getMobileUserProfileDto.OpenRequestCount = (uint)await _requestRepository.CountAsync(request =>
            request.OwnerId == CurrentUser.GetId() &&
            (request.Status == RequestStatus.Pending || request.Status == RequestStatus.Processing));

        return getMobileUserProfileDto;
    }

    [Authorize]
    [RemoteService(false)]
    public Task SetProfilePictureAsync(IRemoteStreamContent profilePictureFile)
    {
        return _identityUserProfileManager.SetProfilePicture(
            CurrentUser.GetId(),
            profilePictureFile
        );
    }

    [Authorize]
    public async Task<IRemoteStreamContent?> GetProfilePictureAsync()
    {
        return await _identityUserProfileManager.GetProfilePicture((Guid)CurrentUser?.Id!);
    }

    [Authorize]
    public async Task UpdateEmailAsync(UpdateMobileUserEmailDto input)
    {
        await _identityUserProfileManager.UpdateEmailAsync(CurrentUser.Id!.Value, input.Email, CultureInfo.CurrentCulture.Name);
    }

    [Authorize]
    public async Task<GetUserLocaleDto> GetLocaleAsync()
    {
        var userId = CurrentUser.GetId();
        var language = await _identityUserProfileManager.GetPreferredLanguageAsync(userId);

        return new GetUserLocaleDto
        {
            Language = language
        };
    }

    [Authorize]
    public virtual async Task SetLocaleAsync(SetUserLocaleDto input)
    {
        var userId = CurrentUser.GetId();
        await _identityUserProfileManager.SetPreferredLanguageAsync(userId, input.Language);
    }

    [Authorize]
    public async Task DeleteAsync()
    {
        var customerUser = await IdentityUserManager.FindByIdAsync(CurrentUser.GetId().ToString())
            ?? throw new EntityNotFoundException(nameof(IdentityUser));

        await GoTrackIdentityUserManager.DeleteAsync(customerUser);
    }
}