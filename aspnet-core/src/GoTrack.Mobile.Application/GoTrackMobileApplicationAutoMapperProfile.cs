using System.Drawing;
using System.Linq;
using AutoMapper;
using GoTrack.Addresses;
using GoTrack.AlertDefinitions;
using GoTrack.AlertDefinitions.DisassembleTrackingDevices;
using GoTrack.AlertDefinitions.ExceedingSpeedAlertDefinitions;
using GoTrack.AlertDefinitions.JobTimeAlertDefinitions;
using GoTrack.AlertDefinitions.ZoneAlertDefinitions.EnteringZoneAlertDefinitions;
using GoTrack.AlertDefinitions.ZoneAlertDefinitions.ExitingZoneAlertDefinitions;
using GoTrack.Alerts.AlertLogs;
using GoTrack.GeoZones;
using GoTrack.Identity;
using GoTrack.Localization;
using GoTrack.Mobile.Addresses;
using GoTrack.Mobile.AlertDefinitions;
using GoTrack.Mobile.AlertDefinitions.DisassembleTrackingDevices.DTOs;
using GoTrack.Mobile.AlertDefinitions.DTOs;
using GoTrack.Mobile.AlertDefinitions.ExceedingSpeedAlertDefinitions.DTOs;
using GoTrack.Mobile.AlertDefinitions.JobTimeAlertDefinitions.DTOs;
using GoTrack.Mobile.AlertDefinitions.ZoneAlertDefinitions.EnteringZoneAlertDefinitions.DTOs;
using GoTrack.Mobile.AlertDefinitions.ZoneAlertDefinitions.ExitingZoneAlertDefinitions.DTOs;
using GoTrack.Mobile.AlertLogs.DTOs;
using GoTrack.Mobile.Coordinates.DTOs;
using GoTrack.Mobile.GeoZones;
using GoTrack.Mobile.MobileIdentityUsers;
using GoTrack.Mobile.Monitoring.DTOs.DeviceHistory;
using GoTrack.Mobile.Monitoring.DTOs.LiveLocations;
using GoTrack.Mobile.Observations.DTOs;
using GoTrack.Mobile.Payments.Bills.DTOs;
using GoTrack.Mobile.Requests;
using GoTrack.Mobile.Requests.AccountSubscriptionRequests;
using GoTrack.Mobile.Requests.AccountSubscriptionRequests.BusinessAccountSubscriptionRequests;
using GoTrack.Mobile.Requests.AccountSubscriptionRequests.PersonalAccountSubscriptionRequests;
using GoTrack.Mobile.Requests.IncreaseUserCountRequests.DTOs;
using GoTrack.Mobile.Requests.RenewTrackAccountSubscriptions.DTOs;
using GoTrack.Mobile.Requests.SmsBundleRenewalRequests.DTOs;
using GoTrack.Mobile.Routes.DTOs;
using GoTrack.Mobile.SmsBundles.DTOs;
using GoTrack.Mobile.StopPoints.DTOs;
using GoTrack.Mobile.TrackAccounts.DTOs;
using GoTrack.Mobile.TrackAccounts.TrackAccountSubscriptions.DTOs;
using GoTrack.Mobile.TripTemplates.DTOs;
using GoTrack.Mobile.UserTrackAccountAssociations.DTOs;
using GoTrack.Mobile.VehicleGroups;
using GoTrack.Mobile.Vehicles.DTOs;
using GoTrack.Observations.ObservationViewModels;
using GoTrack.Payments.Bills;
using GoTrack.PolyLines;
using GoTrack.Requests;
using GoTrack.Requests.AccountSubscriptionRequests;
using GoTrack.Requests.AccountSubscriptionRequests.BusinessAccountSubscriptionRequests;
using GoTrack.Requests.AccountSubscriptionRequests.PersonalAccountSubscriptionRequests;
using GoTrack.Requests.IncreaseUserCountRequests;
using GoTrack.Requests.RenewTrackAccountSubscriptions;
using GoTrack.Requests.SmsBundleRenewalRequests;
using GoTrack.Routes;
using GoTrack.Routes.RouteViewModels;
using GoTrack.SmsBundles;
using GoTrack.StopPoints;
using GoTrack.TrackAccounts;
using GoTrack.TripTemplates;
using GoTrack.TripTemplates.TripTemplateViewModels;
using GoTrack.Users;
using GoTrack.UserTrackAccountAssociations;
using GoTrack.VehicleGroups;
using GoTrack.Vehicles;
using Microsoft.Extensions.Localization;
using Volo.Abp.FeatureManagement;
using Volo.Abp.Identity;
using Volo.Abp.Localization;
using Warp10Abstraction.Models;
using GoTrack.TrackAccounts.TrackAccountSubscriptions;
using GoTrack.Requests.AddVehiclesRequests;
using GoTrack.Mobile.Requests.AddVehiclesRequests.DTOs;
using Notify.Notifications;
using GoTrack.Mobile.Notifications.DTOs;
using Notify.Provider.FCM;
using GoTrack.AlertDefinitions.RouteAlertDefinitions.ExitingRouteAlertDefinitions;
using GoTrack.Mobile.AlertDefinitions.ExitingRouteAlertDefinitions.DTOs;
using GoTrack.Mobile.Payments.Discounts.DTOs;
using GoTrack.Mobile.SubscriptionPlans.DTOs;
using GoTrack.Payments.Discounts;
using GoTrack.SubscriptionPlans;

namespace GoTrack.Mobile;

public class GoTrackMobileApplicationAutoMapperProfile : Profile
{
    public GoTrackMobileApplicationAutoMapperProfile()
    {
        CreateMap<IdentityUser, GetMobileUserProfileDto>()
            .ForMember(dto => dto.FirstName, expression => expression.MapFrom(user => user.Name))
            .ForMember(dto => dto.LastName, expression => expression.MapFrom(user => user.Surname))
            .ForMember(dto => dto.Email,
                expression => expression.MapFrom(profile => // todo find way to make Email null
                    profile.Email.Contains(UsersConstants.UserEmptyEmailSuffix) ? null : profile.Email))
            .ForMember(dto => dto.IsVerified,
                expression => expression.MapFrom(profile => profile.EmailConfirmed))
            ;

        CreateMap<IdentityUserProfile, GetMobileUserProfileDto>()
            .ForMember(dto => dto.FirstName, expression => expression.MapFrom(profile => profile.User.Name))
            .ForMember(dto => dto.LastName, expression => expression.MapFrom(profile => profile.User.Surname))
            .ForMember(dto => dto.PhoneNumber, expression => expression.MapFrom(profile => profile.User.PhoneNumber))
            .ForMember(dto => dto.Email,
                expression => expression.MapFrom(profile => // todo find way to make Email null
                    profile.User.Email.Contains(UsersConstants.UserEmptyEmailSuffix) ? null : profile.User.Email))
            .ForMember(dto => dto.IsVerified,
                expression => expression.MapFrom(profile => profile.User.EmailConfirmed))
            ;

        CreateMap<Address, AddressDto>()
            .ForMember(dest => dest.GovernorateDisplayName,
                opt => opt.ConvertUsing<AddressLocalizationResolver, string>(src => src.Governorate))
            .ForMember(dest => dest.CityDisplayName,
                opt => opt.ConvertUsing<AddressLocalizationResolver, string>(src => src.City))
            .ForMember(dest => dest.CountryDisplayName,
                opt => opt.ConvertUsing<AddressLocalizationResolver, string>(src => src.Country));

        CreateMap<Request, RequestDto>();
        CreateMap<RequestNote, RequestNoteDto>();
        CreateMap<BusinessAccountSubscriptionRequest, BusinessAccountSubscriptionRequestDetailsDto>()
            .ForMember(dest => dest.Stage, opt => opt.MapFrom(src => src.AccountSubscriptionRequestStage))
            .ForMember(
                dest => dest.SubscriptionPlanLocalizedName,
                opt => opt.ConvertUsing<GoTrackLocalizationResolver, string>(src => src.SubscriptionPlanKey)
            );


        CreateMap<SubscriptionVehicleInfo, SubscriptionVehicleInfoDto>()
            .ForMember(
                dest => dest.ColorHex,
                opt => opt.MapFrom(src => ConvertColorToHexColor(src.Color))
            );

        CreateMap<TrackAccount, TrackAccountDto>()
            .ForMember(dest => dest.UserTrackAccountAssociation,
                opt =>
                    opt.MapFrom(src => src.UserTrackAccountAssociations.First())
            );

        CreateMap<TrackAccount, TrackAccountDetailsDto>();

        CreateMap<PersonalAccountSubscriptionRequest, PersonalAccountSubscriptionRequestDetailsDto>()
            .ForMember(dest => dest.Stage, opt => opt.MapFrom(src => src.AccountSubscriptionRequestStage))
            .ForMember(
                dest => dest.SubscriptionPlanLocalizedName,
                opt => opt.ConvertUsing<GoTrackLocalizationResolver, string>(src => src.SubscriptionPlanKey)
            );


        CreateMap<Vehicle, VehicleDto>()
            .ForMember(dest => dest.ColorHex,
                opt => opt.MapFrom(src => ConvertColorToHexColor(src.Color))
            )
            .ForMember(dest => dest.LicensePlateSubClass,
                opt => opt.MapFrom(src => src.LicensePlate.SubClass)
            )
            .ForMember(dest => dest.LicensePlateSerial,
                opt => opt.MapFrom(src => src.LicensePlate.Serial)
            );

        CreateMap<AlertDefinition, AlertDefinitionDto>();
        CreateMap<EnteringZoneAlertDefinition, EnteringZoneAlertDefinitionDto>();
        CreateMap<ExceedingSpeedAlertDefinition, ExceedingSpeedAlertDefinitionDto>();
        CreateMap<ExitingZoneAlertDefinition, ExitingZoneAlertDefinitionDto>();
        CreateMap<JobTimeAlertDefinition, JobTimeAlertDefinitionDto>();
        CreateMap<ExitingRouteAlertDefinition, ExitingRouteAlertDefinitionDto>();

        CreateMap<GeoZone, GeoZoneDto>();
        CreateMap<GeoZone, GeoZoneDetailsDto>();
        CreateMap<PolyLine, PolyLineDto>();
        CreateMap<VehicleGroup, VehicleGroupDto>();
        CreateMap<VehicleGroup, VehicleGroupDetailsDto>();

        CreateMap<WarpGTS, WarpGTSDto>().ForMember(dest => dest.LastActivity,
            opt => opt.MapFrom(src => src.GetLastActivity()));
        CreateMap<WarpGTSValue, WarpGTSValueDto>().ForMember(dest => dest.DateTime,
            opt => opt.MapFrom(src => src.GetDateTime()));

        CreateMap<WarpGTS, WarpGtsHistoryDto>().ForMember(dest => dest.LastActivity,
            opt => opt.MapFrom(src => src.GetLastActivity()));

        CreateMap<WarpGTSValue, WarpGtsHistoryValueDto>().ForMember(dest => dest.DateTime,
            opt => opt.MapFrom(src => src.GetDateTime()));

        CreateMap<UserTrackAccountAssociation, UserTrackAccountAssociationDto>()
            .ForMember(dest => dest.PhoneNumber,
                opt => opt.MapFrom(src => src.PhoneNumber == null ? null : src.PhoneNumber.ToString())
            )
            ;

        CreateMap<ObservationViewModel, ObservationViewModelDto>()
            .ForMember(
                dest => dest.PhoneNumber,
                opt => opt.MapFrom(src => src.PhoneNumber.ToString())
            )
            ;

        CreateMap<ObservationViewModel, ObservationViewModelWithAuditingDto>()
            .ForMember(
                dest => dest.PhoneNumber,
                opt => opt.MapFrom(src => src.PhoneNumber.ToString())
            )
            ;

        CreateMap<VehicleViewModel, VehicleViewModelWithAuditingDto>()
            .ForMember(
                dest => dest.LicensePlateSerial,
                opt => opt.MapFrom(src => src.LicensePlate.Serial)
            )
            .ForMember(
                dest => dest.LicensePlateSubClass,
                opt => opt.MapFrom(src => src.LicensePlate.SubClass)
            )
            .ForMember(
                dest => dest.ColorHex,
                opt => opt.MapFrom(src => ConvertColorToHexColor(src.Color))
            )
            ;

        CreateMap<Route, RouteDto>()
            .ForMember(
                dest => dest.Color,
                opt => opt.MapFrom(src => ConvertColorToHexColor(src.Color))
            )
            .ForMember(
                dest => dest.Line,
                opt => opt.MapFrom(src => src.Line.Coordinates
                    .Select(point =>
                        new CoordinateDto
                        {
                            LongitudeX = point.X,
                            LatitudeY = point.Y
                        }
                    )
                )
            )
            .ForMember(
                dest => dest.StartPoint,
                opt => opt.MapFrom(src =>
                    new CoordinateDto
                    {
                        LongitudeX = src.StartPoint.X,
                        LatitudeY = src.StartPoint.Y
                    }
                )
            )
            .ForMember(
                dest => dest.EndPoint,
                opt => opt.MapFrom(src =>
                    new CoordinateDto
                    {
                        LongitudeX = src.EndPoint.X,
                        LatitudeY = src.EndPoint.Y
                    }
                )
            )
            ;


        CreateMap<RouteViewModel, RouteViewModelDto>()
            .ForMember(
                dest => dest.Color,
                opt => opt.MapFrom(src => ConvertColorToHexColor(src.Color))
            )
            .ForMember(
                dest => dest.Line,
                opt => opt.MapFrom(src => src.Line.Coordinates
                    .Select(point =>
                        new CoordinateDto
                        {
                            LongitudeX = point.X,
                            LatitudeY = point.Y
                        }
                    )
                )
            )
            .ForMember(
                dest => dest.StartPoint,
                opt => opt.MapFrom(src =>
                    new CoordinateDto
                    {
                        LongitudeX = src.StartPoint.X,
                        LatitudeY = src.StartPoint.Y
                    }
                )
            )
            .ForMember(
                dest => dest.EndPoint,
                opt => opt.MapFrom(src =>
                    new CoordinateDto
                    {
                        LongitudeX = src.EndPoint.X,
                        LatitudeY = src.EndPoint.Y
                    }
                )
            )
            ;

        CreateMap<StopPoint, StopPointDto>()
            .ForMember(
                dest => dest.Color,
                opt => opt.MapFrom(src => ConvertColorToHexColor(src.Color))
            )
            .ForMember(
                dest => dest.Point,
                opt => opt.MapFrom(src =>
                    new CoordinateDto
                    {
                        LongitudeX = src.Point.X,
                        LatitudeY = src.Point.Y
                    }
                )
            )
            .ForMember(
                dest => dest.Color,
                opt => opt.MapFrom(src => ConvertColorToHexColor(src.Color))
            )
            ;

        CreateMap<TripTemplate, TripTemplateDto>();
        CreateMap<TripTemplateViewModel, TripTemplateViewModelDto>();

        CreateMap<FeatureNameValue, FeatureDto>();
        CreateMap<AlertLog, AlertLogDto>();
        CreateMap<DisassembleTrackingDeviceAlertDefinition, DisassembleTrackingDevicesDto>();
        CreateMap<SmsBundle, SmsBundleDto>();
        CreateMap<BillLineItem, BillLineItemDto>()
            .ForMember(
                dest => dest.PricingItemDisplayName, 
                opt => opt.ConvertUsing<GoTrackLocalizationResolver, string>(src => src.PricingItemKey)
            );
        CreateMap<Bill, BillDto>()
            .ForMember(dest => dest.Total, opt => opt.MapFrom(src => src.BillableAmount))
            .ForMember(dest => dest.TotalBeforeDiscounts, opt => opt.MapFrom(src => src.CalculateTotalBeforeDiscounts()));
        CreateMap<AppliedDiscount, AppliedDiscountDto>();
        CreateMap<IncreaseUserCountRequest, IncreaseUserCountRequestDto>();
        CreateMap<SmsBundleRenewalRequest, SmsBundleRenewalRequestDto>();
        CreateMap<TrackAccountSubscription, TrackAccountSubscriptionDetailDto>()
            .ForMember(
                dest => dest.SubscriptionPlanLocalizedName,
                opt => opt.ConvertUsing<GoTrackLocalizationResolver, string>(src => src.SubscriptionPlanKey)
            );
        CreateMap<TrackAccountSubscription, TrackAccountSubscriptionDto>()
            .ForMember(
                dest => dest.SubscriptionPlanLocalizedName,
                opt => opt.ConvertUsing<GoTrackLocalizationResolver, string>(src => src.SubscriptionPlanKey)
            );
        CreateMap<TrackAccountRequest, RequestDto>();
        CreateMap<RenewSubscriptionRequest, RenewSubscriptionRequestDto>();
        CreateMap<AddVehiclesRequest, AddVehiclesRequestDto>();

        CreateMap<RenewSubscriptionRequest, RenewSubscriptionRequestDetailsDto>()
            .ForMember(dest => dest.NewTrackVehiclesCount, opt => opt.MapFrom(x => x.NewTrackVehicles.Count))
            .ForMember(dest => dest.RemoveTrackVehiclesCount, opt => opt.MapFrom(x => x.RemoveTrackVehicles.Count))
            .ForMember(dest => dest.RemoveUsersCount, opt => opt.MapFrom(x => x.RemoveUsers.Count))
            .ForMember(
                dest => dest.SubscriptionPlanLocalizedName,
                opt => opt.ConvertUsing<GoTrackLocalizationResolver, string>(src => src.SubscriptionPlanKey)
            );

        CreateMap<Notification, NotificationDto>()
            .ForMember(
                dest => dest.Title,
                opt => opt.MapFrom(src => src.GetFCMTitle())
            )
            .ForMember(
                dest => dest.Body,
                opt => opt.MapFrom(src => src.GetFCMBody())
            );
        /* You can configure your AutoMapper mapping configuration here.
         * Alternatively, you can split your mapping configurations
         * into multiple profile classes for a better organization. */
        
        CreateMap<Discount, SubscriptionDurationDiscountDto>()
            .ForMember(dest => dest.Months, 
                opt => opt.MapFrom(x => int.Parse(x.DiscountCriteriaList.First().SpecificationValue)));

        CreateMap<SubscriptionPlanDefinition, SubscriptionPlanDto>()
            .ForMember(
                dest => dest.LocalizedName,
                opt => opt.ConvertUsing<LocalizationResolver, ILocalizableString>(src => src.DisplayName)
            )
            .ForMember(
                dest => dest.RelatedSubscriptionDurationMonths,
                opt => opt.AllowNull()
            );
    }

    private class LocalizationResolver : IValueConverter<ILocalizableString, string>
    {
        private readonly IStringLocalizerFactory StringLocalizerFactory;

        public LocalizationResolver(IStringLocalizerFactory stringLocalizerFactory)
        {
            StringLocalizerFactory = stringLocalizerFactory;
        }

        public string Convert(ILocalizableString sourceMember, ResolutionContext context)
        {
            return sourceMember.Localize(StringLocalizerFactory);
        }
    }

    private static string ConvertColorToHexColor(Color color)
    {
        return $"0xFF{color.R:X2}{color.G:X2}{color.B:X2}";
    }

    private class AddressLocalizationResolver : IValueConverter<string, string>
    {
        private readonly IStringLocalizerFactory StringLocalizerFactory;

        public AddressLocalizationResolver(IStringLocalizerFactory stringLocalizerFactory) =>
            StringLocalizerFactory = stringLocalizerFactory;

        public string Convert(string sourceMember, ResolutionContext context) =>
            new LocalizableString(typeof(GeoNodeResource), sourceMember).Localize(StringLocalizerFactory);
    }

    private class GoTrackLocalizationResolver : IValueConverter<string, string>
    {
        private readonly IStringLocalizerFactory StringLocalizerFactory;

        public GoTrackLocalizationResolver(IStringLocalizerFactory stringLocalizerFactory)
        {
            StringLocalizerFactory = stringLocalizerFactory;
        }

        public string Convert(string sourceMember, ResolutionContext context) =>
                    new LocalizableString(typeof(GoTrackResource), sourceMember).Localize(StringLocalizerFactory);

    }
}