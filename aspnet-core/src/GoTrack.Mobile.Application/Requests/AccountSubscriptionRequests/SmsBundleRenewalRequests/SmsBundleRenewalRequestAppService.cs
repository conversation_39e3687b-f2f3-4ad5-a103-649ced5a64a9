using System;
using System.Linq;
using System.Threading.Tasks;
using GoTrack.Identity;
using GoTrack.Mobile.Payments.Bills.DTOs;
using GoTrack.Mobile.Requests.SmsBundleRenewalRequests;
using GoTrack.Mobile.Requests.SmsBundleRenewalRequests.DTOs;
using GoTrack.Payments;
using GoTrack.Payments.Bills;
using GoTrack.Requests;
using GoTrack.Requests.SmsBundleRenewalRequests;
using GoTrack.Settings;
using GoTrack.TrackAccounts.TrackAccountSubscriptions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.DependencyInjection;
using Volo.Abp;
using Volo.Abp.Domain.Entities;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Settings;
using Volo.Abp.Uow;

namespace GoTrack.Mobile.Requests.AccountSubscriptionRequests.SmsBundleRenewalRequests;

public class SmsBundleRenewalRequestAppService : GoTrackMobileAppService, ISmsBundleRenewalRequestAppService
{
    private readonly ISmsBundleRenewalManager _bundleRenewalManager;
    private readonly IRepository<SmsBundleRenewalRequest, Guid> _smsBundleRenewalRequestRepository;
    private readonly IRepository<UserFatoraPayment, Guid> _userFatoraPaymentRepository;
    private readonly ISettingProvider _settingProvider;
    protected BillManager BillManager =>
        LazyServiceProvider.LazyGetRequiredService<BillManager>();

    protected IUserFatoraPaymentManager UserFatoraPaymentManager =>
        LazyServiceProvider.LazyGetRequiredService<IUserFatoraPaymentManager>();

    protected TrackAccountSubscriptionManager TrackAccountSubscriptionManager =>
        LazyServiceProvider.LazyGetRequiredService<TrackAccountSubscriptionManager>();

    protected SmsBundleRenewalRequestBillPlanFactory SmsBundleRenewalRequestBillPlanFactory =>
        LazyServiceProvider.LazyGetRequiredService<SmsBundleRenewalRequestBillPlanFactory>();
    public SmsBundleRenewalRequestAppService(
        ISmsBundleRenewalManager bundleRenewalManager,
        IRepository<SmsBundleRenewalRequest, Guid> smsBundleRenewalRequestRepository,
        IRepository<UserFatoraPayment, Guid> userFatoraPaymentRepository, ISettingProvider settingProvider)
    {
        _bundleRenewalManager = bundleRenewalManager;
        _smsBundleRenewalRequestRepository = smsBundleRenewalRequestRepository;
        _userFatoraPaymentRepository = userFatoraPaymentRepository;
        _settingProvider = settingProvider;
    }

    [Authorize]
    [TrackAccountAuthorize]
    public async Task<Guid> CreateAsync(CreateSmsBundleRenewalRequestDto createSmsBundleRenewalRequestDto)
    {
        var request = await CreateRequestAsync(createSmsBundleRenewalRequestDto.SmsBundleId);

        return request.Id;
    }


    [Authorize]
    [TrackAccountAuthorize]
    public async Task<SmsBundleRenewalRequestDto> GetAsync(Guid requestId)
    {
        using var _ = DataFilter.Disable<IHostTenantUserFilter>();
        using var __ = DataFilter.Enable<ICustomerUserFilter>();

        var renewalRequestQuery = await _smsBundleRenewalRequestRepository.WithDetailsAsync(request => request.Owner);
        renewalRequestQuery = renewalRequestQuery.Where(request => request.Id == requestId);

        var renewalRequest = await AsyncExecuter.SingleOrDefaultAsync(renewalRequestQuery)
                             ?? throw new EntityNotFoundException(typeof(SmsBundleRenewalRequest), requestId);

        var renewalRequestDto = ObjectMapper.Map<SmsBundleRenewalRequest, SmsBundleRenewalRequestDto>(renewalRequest);

        var paymentQuery = await _userFatoraPaymentRepository.GetQueryableAsync();
        paymentQuery = paymentQuery
            .Where(payment => payment.Id == requestId && payment.PaymentStatus == PaymentStatus.Pending)
            .OrderByDescending(payment => payment.CreationTime);

        var pendingPayment = await AsyncExecuter.FirstOrDefaultAsync(paymentQuery);

        renewalRequestDto.PaymentUrl = pendingPayment?.Url;

        return renewalRequestDto;
    }

    [Authorize]
    [TrackAccountAuthorize]
    public async Task<BillDto> CreateTempBillAsync(CreateSmsBundleRenewalRequestDto input)
    {
        var trackAccountSubscription = await TrackAccountSubscriptionManager
            .GetCurrentActiveTrackAccountSubscriptionAsync(CurrentTrackAccount.GetId());

        var remainingMonths = await TrackAccountSubscriptionManager.GetRemainingMonths(trackAccountSubscription.Id);

        var billPlan = await SmsBundleRenewalRequestBillPlanFactory.GenerateBillPlan(
            new SmsBundleRenewalRequestBillPlanInput(
                GuidGenerator.Create(),
                CurrentUser.Id!.Value,
                input.SmsBundleId,
                remainingMonths
            )
        );

        var bill = await BillManager.CreateTempBillAsync(billPlan);

        return ObjectMapper.Map<Bill, BillDto>(bill);
    }

    [Authorize]
    [TrackAccountAuthorize]
    public async Task<string> CreatePaymentAsync(CreateAccountRequestPaymentDto input)
    {
        var isPayEnabled = await _settingProvider.GetAsync<bool>(
            GoTrackSettings.SmsBundleRenewalRequestFatoraPayEnabled,
            GoTrackSettings.SmsBundleRenewalRequestPayEnabledDefault);

        if (isPayEnabled is false)
        {
            throw new BusinessException(GoTrackDomainErrorCodes.PaymentDisabled);
        }
        
        var request = await _smsBundleRenewalRequestRepository.GetAsync(input.RequestId);

        if (request.SmsBundleRenewalStage is not SmsBundleRenewalStage.Payment)
            throw new UserFriendlyException(L[GoTrackApplicationErrorCodes.PaymentNotAllowed]);

        var bill = await BillManager.GetByRequestIdAsync(request.Id);

        return await UserFatoraPaymentManager.PayAsync(input.RequestId,
            CurrentUser.Id!.Value,
            input.Language,
            (int)bill.BillableAmount,
            input.SavedCards,
            input.CallBackUrl,
            null
        );
    }


    private async Task<SmsBundleRenewalRequest> CreateRequestAsync(Guid smsBundleId)
    {
        var trackAccountSubscription = await TrackAccountSubscriptionManager.GetCurrentActiveTrackAccountSubscriptionAsync(
            CurrentTrackAccount.GetId()
        );

        return await _bundleRenewalManager.CreateAsync(
            CurrentUser.Id!.Value,
            trackAccountSubscription.Id,
            smsBundleId,
            trackAccountSubscription.TrackAccountId
        );
    }
}