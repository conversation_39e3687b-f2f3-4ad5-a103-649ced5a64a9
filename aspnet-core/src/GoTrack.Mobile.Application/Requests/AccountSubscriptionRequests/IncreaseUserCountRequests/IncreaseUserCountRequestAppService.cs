using System;
using System.Linq;
using System.Threading.Tasks;
using GoTrack.Identity;
using GoTrack.Mobile.Payments.Bills.DTOs;
using GoTrack.Mobile.Requests.IncreaseUserCountRequests;
using GoTrack.Mobile.Requests.IncreaseUserCountRequests.DTOs;
using GoTrack.Payments;
using GoTrack.Payments.Bills;
using GoTrack.Requests;
using GoTrack.Requests.IncreaseUserCountRequests;
using GoTrack.Settings;
using GoTrack.TrackAccounts.TrackAccountSubscriptions;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.DependencyInjection;
using Volo.Abp;
using Volo.Abp.Domain.Entities;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Settings;
using Volo.Abp.Uow;
using Volo.Abp.Users;

namespace GoTrack.Mobile.Requests.AccountSubscriptionRequests.IncreaseUserCountRequests;

public class IncreaseUserCountRequestAppService : GoTrackMobileAppService, IIncreaseUserCountRequestAppService
{
    private readonly IRepository<UserFatoraPayment, Guid> _userFatoraPaymentRepository;
    private readonly IRepository<IncreaseUserCountRequest, Guid> _increaseUserCountRequestRepository;
    private readonly IIncreaseUserCountRequestManager _increaseUserCountRequestManager;
    private readonly IRepository<TrackAccountSubscription, Guid> _trackAccountSubscriptionRepository;
    protected TrackAccountSubscriptionManager TrackAccountSubscriptionManager =>
        LazyServiceProvider.GetRequiredService<TrackAccountSubscriptionManager>();

    protected BillManager BillManager =>
        LazyServiceProvider.LazyGetRequiredService<BillManager>();
    protected UserFatoraPaymentManager UserFatoraPaymentManager =>
        LazyServiceProvider.LazyGetRequiredService<UserFatoraPaymentManager>();

    protected IncreaseUserCountRequestBillPlanFactory IncreaseUserCountRequestBillPlanFactory =>
        LazyServiceProvider.LazyGetRequiredService<IncreaseUserCountRequestBillPlanFactory>();

    private readonly ISettingProvider _settingProvider;

    public IncreaseUserCountRequestAppService(IRepository<UserFatoraPayment, Guid> userFatoraPaymentRepository,
        IRepository<IncreaseUserCountRequest, Guid> increaseUserCountRequestRepository,
        IIncreaseUserCountRequestManager increaseUserCountRequestManager,
        IRepository<TrackAccountSubscription, Guid> trackAccountSubscriptionRepository, ISettingProvider settingProvider)
    {
        _userFatoraPaymentRepository = userFatoraPaymentRepository;
        _increaseUserCountRequestRepository = increaseUserCountRequestRepository;
        _increaseUserCountRequestManager = increaseUserCountRequestManager;
        _trackAccountSubscriptionRepository = trackAccountSubscriptionRepository;
        _settingProvider = settingProvider;
    }

    [Authorize]
    [TrackAccountAuthorize]
    public async Task<Guid> CreateAsync(CreateIncreaseUserCountRequestDto createIncreaseUserCountRequestDto)
    {
        var request = await CreateRequestAsync(createIncreaseUserCountRequestDto.UserCount, CurrentUser.GetId(), CurrentTrackAccount.GetId());

        return request.Id;
    }

    [Authorize]
    [TrackAccountAuthorize]
    public async Task<IncreaseUserCountRequestDto> GetAsync(Guid id)
    {
        using var disableHostTenantFilter = DataFilter.Disable<IHostTenantUserFilter>();
        using var enableCustomerUserFilter = DataFilter.Enable<ICustomerUserFilter>();

        var requestQuery = await _increaseUserCountRequestRepository.WithDetailsAsync(request => request.Owner);
        requestQuery = requestQuery.Where(request => request.Id == id);

        var increaseUserRequest = await AsyncExecuter.SingleOrDefaultAsync(requestQuery)
                                  ?? throw new EntityNotFoundException(typeof(IncreaseUserCountRequest), id);

        var increaseUserRequestDto =
            ObjectMapper.Map<IncreaseUserCountRequest, IncreaseUserCountRequestDto>(increaseUserRequest);

        var paymentQuery = await _userFatoraPaymentRepository.GetQueryableAsync();
        paymentQuery = paymentQuery
            .Where(payment => payment.Id == id && payment.PaymentStatus == PaymentStatus.Pending)
            .OrderByDescending(payment => payment.CreationTime);

        var pendingPayment = await AsyncExecuter.FirstOrDefaultAsync(paymentQuery);

        increaseUserRequestDto.PaymentUrl = pendingPayment?.Url;

        return increaseUserRequestDto;
    }


    [Authorize]
    [TrackAccountAuthorize]
    public async Task<BillDto> CreateTempBillAsync(CreateIncreaseUserCountRequestDto input)
    {
        var trackAccountSubscription = await TrackAccountSubscriptionManager
            .GetCurrentActiveTrackAccountSubscriptionAsync(CurrentTrackAccount.GetId());

        var remainingMonths = await TrackAccountSubscriptionManager.GetRemainingMonths(trackAccountSubscription.Id);

        var billPlan = await IncreaseUserCountRequestBillPlanFactory.GenerateBillPlan(
            new IncreaseUserCountRequestBillPlanInput(
                GuidGenerator.Create(),
                CurrentUser.Id!.Value,
                input.UserCount,
                remainingMonths
            )
        );

        var bill = await BillManager.CreateTempBillAsync(billPlan);

        return ObjectMapper.Map<Bill, BillDto>(bill);
    }

    [Authorize]
    [TrackAccountAuthorize]
    public async Task<string> CreatePaymentAsync(CreateAccountRequestPaymentDto input)
    {
        var isPayEnabled = await _settingProvider.GetAsync<bool>(
            GoTrackSettings.IncreaseUserCountRequestFatoraPayEnabled,
            GoTrackSettings.IncreaseUserCountRequestPayEnabledDefault);

        if (isPayEnabled is false)
        {
            throw new BusinessException(GoTrackDomainErrorCodes.PaymentDisabled);
        }
        
        var request = await _increaseUserCountRequestRepository.GetAsync(input.RequestId);

        if (request.IncreaseUserCountRequestStage is not IncreaseUserCountRequestStage.Payment)
            throw new UserFriendlyException(L[GoTrackApplicationErrorCodes.PaymentNotAllowed]);

        var bill = await BillManager.GetByRequestIdAsync(request.Id);

        return await UserFatoraPaymentManager.PayAsync(input.RequestId,
            CurrentUser.Id!.Value,
            input.Language,
            (int)bill.BillableAmount,
            input.SavedCards,
            input.CallBackUrl,
            null
        );
    }


    private async Task<IncreaseUserCountRequest> CreateRequestAsync(int userCount, Guid requestOwnerId, Guid trackAccountId)
    {
        var trackAccountSubscription = await TrackAccountSubscriptionManager
            .GetCurrentActiveTrackAccountSubscriptionAsync(trackAccountId);

        var request = await _increaseUserCountRequestManager.CreateAsync(
            requestOwnerId,
            trackAccountSubscription.Id,
            userCount,
            trackAccountId
        );

        return request;
    }

}