using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Drawing;
using System.Linq;
using System.Threading.Tasks;
using GoTrack.Addresses;
using GoTrack.Mobile.Payments.Bills.DTOs;
using GoTrack.Payments;
using GoTrack.Payments.Bills;
using GoTrack.Payments.PromoCodes;
using GoTrack.Requests;
using GoTrack.Requests.AccountSubscriptionRequests;
using GoTrack.Requests.AccountSubscriptionRequests.PersonalAccountSubscriptionRequests;
using GoTrack.Settings;
using GoTrack.SubscriptionPlans;
using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.DependencyInjection;
using Volo.Abp;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Features;
using Volo.Abp.Settings;
using Volo.Abp.Uow;
using Volo.Abp.Validation;

namespace GoTrack.Mobile.Requests.AccountSubscriptionRequests.PersonalAccountSubscriptionRequests;

//[RequiresFeature(GoTrackFeatureDefinitions.SubscriptionRequestManagement)]
public class PersonalAccountSubscriptionRequestAppService : GoTrackMobileAppService,
    IPersonalAccountSubscriptionRequestAppService
{
    private readonly IRepository<PersonalAccountSubscriptionRequest, Guid> _personalAccountSubscriptionRequestRepo;
    private readonly IRepository<RequestNote, Guid> _requestNoteRepository;
    private readonly IUserFatoraPaymentManager _userFatoraPaymentManager;
    private readonly IRepository<UserFatoraPayment, Guid> _userFatoraPaymentRepository;
    private readonly IPersonalAccountSubscriptionRequestManager _subscriptionRequestManager;
    private readonly PersonalAccountSubscriptionRequestBillPlanFactory _personalAccountSubscriptionRequestBillPlanFactory;
    private readonly ISettingProvider _settingProvider;
    private readonly IAddressManager _addressManager;

    private BillManager BillManager => LazyServiceProvider.GetRequiredService<BillManager>();
    protected PromoCodeManager PromoCodeManager =>
        LazyServiceProvider.LazyGetRequiredService<PromoCodeManager>();
    
    protected SubscriptionPlanDefinitionStore SubscriptionPlanDefinitionStore 
        => LazyServiceProvider.LazyGetRequiredService<SubscriptionPlanDefinitionStore>();
    
    public PersonalAccountSubscriptionRequestAppService(
        IRepository<PersonalAccountSubscriptionRequest, Guid> personalAccountSubscriptionRequestRepository,
        IRepository<RequestNote, Guid> requestNoteRepository,
        IUserFatoraPaymentManager userFatoraPaymentManager,
        IRepository<UserFatoraPayment, Guid> userFatoraPaymentRepository,
        IPersonalAccountSubscriptionRequestManager subscriptionRequestManager, PersonalAccountSubscriptionRequestBillPlanFactory personalAccountSubscriptionRequestBillPlanFactory, ISettingProvider settingProvider, IAddressManager addressManager)
    {
        _personalAccountSubscriptionRequestRepo = personalAccountSubscriptionRequestRepository;
        _requestNoteRepository = requestNoteRepository;
        _userFatoraPaymentManager = userFatoraPaymentManager;
        _userFatoraPaymentRepository = userFatoraPaymentRepository;
        _subscriptionRequestManager = subscriptionRequestManager;
        _personalAccountSubscriptionRequestBillPlanFactory = personalAccountSubscriptionRequestBillPlanFactory;
        _settingProvider = settingProvider;
        _addressManager = addressManager;
    }

    public async Task<BillDto> CreateTempBillAsync(PersonalAccountSubscriptionRequestCreateDto input)
    {
        if (!string.IsNullOrEmpty(input.PromoCode))
        {
            await PromoCodeManager.ValidatePromoCodeAsync(input.PromoCode);
        }
        
        var subscriptionVehicleInfos = input.SubscriptionVehicleInfoCreateDtos
            .Select(dto => new SubscriptionVehicleInfo(
                new SubscriptionVehicleLicensePlate(dto.LicensePlateSubClass, dto.LicensePlateSerial),
                TryGetColor(dto.Color),
                dto.ConsumptionRate,
                dto.NeedsTrackingDevice))
            .ToList();

        var subscriptionPlanDefinition = await SubscriptionPlanDefinitionStore.GetWithValidate(input.SubscriptionPlanKey);

        var billPlan = await _personalAccountSubscriptionRequestBillPlanFactory.GenerateBillPlan(
            new PersonalAccountSubscriptionRequestBillPlanInput(
                GuidGenerator.Create(),
                CurrentUser.Id!.Value,
                subscriptionPlanDefinition,
                input.SubscriptionDurationInMonths,
                subscriptionVehicleInfos.Count(x => x.NeedsTrackingDevice),
                subscriptionVehicleInfos.Count,
                input.SmsBundleId,
                input.PromoCode
            ));

        var bill = await BillManager.CreateTempBillAsync(billPlan);

        return ObjectMapper.Map<Bill, BillDto>(bill);
    }

    [Authorize]
    public async Task<PersonalAccountSubscriptionRequestDetailsDto> GetAsync(Guid id)
    {
        var subscriptionRequest = await _personalAccountSubscriptionRequestRepo.GetAsync(id);

        var requestDetailsDto =
            ObjectMapper.Map<PersonalAccountSubscriptionRequest, PersonalAccountSubscriptionRequestDetailsDto>(
                subscriptionRequest);

        var requestNoteQ = (await _requestNoteRepository.GetQueryableAsync())
            .OrderByDescending(note => note.CreationTime)
            .Where(note => note.RequestId == id);

        requestNoteQ = requestNoteQ.Take(100);

        var requestNotes = await AsyncExecuter.ToListAsync(requestNoteQ);

        requestDetailsDto.RequestNotes = ObjectMapper.Map<List<RequestNote>, List<RequestNoteDto>>(requestNotes);

        var userFatoraPaymentQuery = await _userFatoraPaymentRepository.GetQueryableAsync();

        userFatoraPaymentQuery = userFatoraPaymentQuery
            .Where(x => x.Id == id && x.PaymentStatus == PaymentStatus.Pending)
            .OrderByDescending(x => x.CreationTime);

        var userFatoraPayment = await AsyncExecuter.FirstOrDefaultAsync(userFatoraPaymentQuery);

        requestDetailsDto.PaymentUrl = userFatoraPayment?.Url;
        requestDetailsDto.Price = 10_000;

        return requestDetailsDto;
    }

    [Authorize]
    public virtual async Task<Guid> CreateAsync(PersonalAccountSubscriptionRequestCreateDto createDto)
    {
        var subscriptionVehicleInfos = createDto.SubscriptionVehicleInfoCreateDtos
            .Select(dto => new SubscriptionVehicleInfo(
                new SubscriptionVehicleLicensePlate(dto.LicensePlateSubClass, dto.LicensePlateSerial),
                TryGetColor(dto.Color),
                dto.ConsumptionRate,
                dto.NeedsTrackingDevice))
            .ToList();

        Address? address = null;
        
        if (createDto.Address is not null)
        {
            address = await _addressManager.CreateAsync(createDto.Address.Street, createDto.Address.Area,
                createDto.Address.City, createDto.Address.Governorate, createDto.Address.Country);
        }
            
        var requestId = await _subscriptionRequestManager.CreateAsync(
            CurrentUser.Id!.Value,
            createDto.AccountName,
            TrackerInstallationLocation.OnSite,
            subscriptionVehicleInfos,
            createDto.SubscriptionPlanKey,
            createDto.UserCount,
            createDto.SubscriptionDurationInMonths,
            address,
            createDto.PromoCode,
            createDto.SmsBundleId
        );
        
        return requestId;
    }

    [Authorize]
    public async Task<string> CreatePaymentAsync(CreateAccountRequestPaymentDto input)
    {
        var isPayEnabled = await _settingProvider.GetAsync<bool>(
            GoTrackSettings.PersonalRequestFatoraPayEnabled,
            GoTrackSettings.PersonalRequestPayEnabledDefault);

        if (isPayEnabled is false)
        {
            throw new BusinessException(GoTrackDomainErrorCodes.PaymentDisabled);
        }
        var request = await _personalAccountSubscriptionRequestRepo.GetAsync(input.RequestId);

        if (request.AccountSubscriptionRequestStage is not AccountSubscriptionRequestStage.Payment)
            throw new UserFriendlyException(L[GoTrackApplicationErrorCodes.PaymentNotAllowed]);

        var bill = await BillManager.GetByRequestIdAsync(request.Id);

        return await _userFatoraPaymentManager.PayAsync(input.RequestId,
            CurrentUser.Id!.Value,
            input.Language,
            (int)bill.BillableAmount,
            input.SavedCards,
            input.CallBackUrl,
            null
        );
    }
    

    private static Color TryGetColor(string color)
    {
        try
        {
            return ColorTranslator.FromHtml(color);
        }
        catch (Exception)
        {
            throw new AbpValidationException("", new List<ValidationResult>()
            {
                new("Invalid Color")
            });
        }
    }
}