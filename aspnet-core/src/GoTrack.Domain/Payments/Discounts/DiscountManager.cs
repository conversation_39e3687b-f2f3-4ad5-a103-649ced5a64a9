using GoTrack.Payments.PricingItems;
using GoTrack.Requests;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using GoTrack.Payments.Bills;
using GoTrack.Payments.PromoCodes;
using GoTrack.SubscriptionPlans;
using Microsoft.Extensions.Logging;
using Volo.Abp;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Domain.Services;

namespace GoTrack.Payments.Discounts;

public class DiscountManager : DomainService
{
    private readonly IRepository<Discount, Guid> _discountRepository;
    private readonly IEnumerable<IDiscountSpecification> _specifications;
    protected IRepository<Request, Guid> RequestRepository =>
        LazyServiceProvider.LazyGetRequiredService<IRepository<Request, Guid>>();

    protected StaticPricingItemDefinitionStore StaticPricingItemDefinitionStore =>
        LazyServiceProvider.LazyGetRequiredService<StaticPricingItemDefinitionStore>();

    protected IRepository<Bill, Guid> BillRepository =>
        LazyServiceProvider.LazyGetRequiredService<IRepository<Bill, Guid>>();

    protected IRepository<PromoCode, Guid> PromoCodeRepository =>
        LazyServiceProvider.LazyGetRequiredService<IRepository<PromoCode, Guid>>();

    protected ILogger<DiscountManager> Logger =>
        LazyServiceProvider.LazyGetRequiredService<ILogger<DiscountManager>>();

    public DiscountManager(
        IRepository<Discount, Guid> discountRepository,
        IEnumerable<IDiscountSpecification> specifications)
    {
        _discountRepository = discountRepository;
        _specifications = specifications;
    }

    public async Task<List<AppliedDiscount>> CreateAppliedDiscountForBill(BillPlan billPlan)
    {
        var now = Clock.Now;
        var appliedDiscounts = new List<AppliedDiscount>();

        const int pageSize = 1000;
        var skipCount = 0;
        var hasMoreDiscounts = true;

        while (hasMoreDiscounts)
        {
            var discountBatch = await GetActiveDiscountsBatchAsync(now, skipCount, pageSize);

            if (discountBatch.Count == 0)
            {
                hasMoreDiscounts = false;
                continue;
            }

            var applicableDiscounts = FilterApplicableDiscounts(discountBatch, billPlan);
            appliedDiscounts.AddRange(ApplyDiscounts(applicableDiscounts, billPlan, now));

            skipCount += pageSize;
            hasMoreDiscounts = discountBatch.Count == pageSize;
        }

        return appliedDiscounts;
    }

    private async Task<List<string>> ExtractPromoCodesFromDiscountsAsync(List<AppliedDiscount> appliedDiscounts)
    {
        var promoCodes = new List<string>();

        foreach (var appliedDiscount in appliedDiscounts)
        {
            var discount = await _discountRepository.GetAsync(appliedDiscount.DiscountId);

            var promoCodeCriteria = discount.DiscountCriteriaList
                .FirstOrDefault(c => c.DiscountSpecificationKey == DiscountSpecificationKey.PromoCode);

            if (promoCodeCriteria != null)
            {
                promoCodes.Add(promoCodeCriteria.SpecificationValue);
            }
        }

        return promoCodes;
    }

    private async Task<List<Discount>> GetActiveDiscountsBatchAsync(DateTime now, int skipCount, int pageSize)
    {
        var query = await _discountRepository.GetQueryableAsync();
        query = query.Where(d => (!d.EndDate.HasValue || now < d.EndDate) && now >= d.StartDate)
                     .Skip(skipCount)
                     .Take(pageSize);

        return await AsyncExecuter.ToListAsync(query);
    }

    private List<Discount> FilterApplicableDiscounts(List<Discount> discounts, BillPlan billPlan)
    {
        return discounts.Where(d => d.DiscountCriteriaList.All(c =>
        {
            var specification = _specifications.FirstOrDefault(s => s.SpecificationKey == c.DiscountSpecificationKey);
            return specification is not null && specification.IsSatisfiedBy(c, billPlan);
        })).ToList();
    }

    private List<AppliedDiscount> ApplyDiscounts(List<Discount> applicableDiscounts, BillPlan billPlan, DateTime now)
    {
        var appliedDiscounts = new List<AppliedDiscount>();

        foreach (var discount in applicableDiscounts)
        {
            switch (discount.TargetType)
            {
                case TargetType.Unspecified:
                    break;
                case TargetType.OnTotal:
                    appliedDiscounts.Add(ApplyOnTotalDiscount(discount, billPlan, now));
                    break;
                case TargetType.OnListItem:
                    appliedDiscounts.AddRange(ApplyOnListItemDiscount(discount, billPlan, now));
                    break;
                default:
                    throw new ArgumentOutOfRangeException();
            }
        }

        return appliedDiscounts;
    }

    private AppliedDiscount ApplyOnTotalDiscount(
        Discount discount,
        BillPlan billPlan,
        DateTime appliedAt)
    {
        var totalBillAmount = billPlan.BillLineItems.Sum(li => li.BillableAmount);
        var discountAmount = discount.CalculateDiscountAmount(totalBillAmount);

        return new AppliedDiscount(
            discountId: discount.Id,
            value: discount.Value,
            isPercentage: discount.IsPercentage,
            amount: discountAmount,
            appliedAt: appliedAt,
            discount.TargetType,
            null
        );
    }

    private IEnumerable<AppliedDiscount> ApplyOnListItemDiscount(
        Discount discount,
        BillPlan billPlan,
        DateTime appliedAt)
    {
        foreach (var lineItem in billPlan.BillLineItems)
        {
            if (discount.PricingItemKeys?.Contains(lineItem.PricingItemKey) is false)
                continue;

            var discountAmount = discount.CalculateDiscountAmount(lineItem.BillableAmount);

            yield return new AppliedDiscount(
                discountId: discount.Id,
                value: discount.Value,
                isPercentage: discount.IsPercentage,
                amount: discountAmount,
                appliedAt: appliedAt,
                discount.TargetType,
                lineItem.PricingItemKey
            );
        }
    }

    public async Task<Discount> CreateDiscountAsync(
        Guid id,
        string name,
        TargetType targetType,
        decimal value,
        bool isPercentage,
        DateTime startDate,
        List<DiscountCriteriaViewModel> discountCriteriaViewModels,
        List<string>? pricingItemKeys = null,
        DateTime? endDate = null)
    {
        if (isPercentage && value is <= 0 or > 1)
            throw new BusinessException(GoTrackDomainErrorCodes.DiscountPercentageValueOutOfRange);


        if (endDate is not null && startDate >= endDate)
            throw new BusinessException(GoTrackDomainErrorCodes.DiscountInvalidDateRange);

        //TODO: add duplicated check and intersection

        List<DiscountCriteria> discountCriteriaList = [];
        foreach (var criteriaViewModel in discountCriteriaViewModels)
        {
            var specification = _specifications.FirstOrDefault(s => s.SpecificationKey == criteriaViewModel.DiscountSpecificationKey);

            if (specification is null)
                throw new BusinessException(GoTrackDomainErrorCodes.DiscountSpecificationNotFound);

            specification.CheckIfValidAsync(criteriaViewModel.SpecificationValue);
            discountCriteriaList.Add(new DiscountCriteria(
                criteriaViewModel.DiscountSpecificationKey,
                criteriaViewModel.SpecificationValue
            ));
        }

        if (targetType is TargetType.OnListItem && (pricingItemKeys is null || pricingItemKeys!.Count == 0))
        {
            throw new BusinessException(GoTrackDomainErrorCodes.DiscountPricingItemKeysRequired);
        }

        if (targetType is TargetType.OnListItem && pricingItemKeys!.Any(pricingItemKey => StaticPricingItemDefinitionStore.GetOrNull(pricingItemKey) is null))
        {
            throw new BusinessException(GoTrackDomainErrorCodes.DiscountPricingItemNotFound);
        }


        var discount = new Discount
        (
            id,
            name,
            targetType,
            value,
            isPercentage,
            startDate,
            discountCriteriaList,
            pricingItemKeys,
            endDate
        );

        await _discountRepository.InsertAsync(discount);

        return discount;
    }

    public async Task<Discount> CreateSubscriptionDurationDiscount(
        Guid id,
        decimal value,
        bool isPercentage,
        DateTime startDate,
        DateTime? endDate,
        int durationInMonth)
    {
        return await CreateDiscountAsync
        (
            id,
            "Subscription Duration Discount",
            TargetType.OnListItem,
            value,
            isPercentage,
            startDate,
            [new DiscountCriteriaViewModel(DiscountSpecificationKey.Duration,durationInMonth.ToString()),
            new DiscountCriteriaViewModel(DiscountSpecificationKey.RequestType,"BusinessAccountSubscription, PersonalAccountSubscription, RenewSubscription")],
            [
                SubscriptionPlanKeys.Silver,
                SubscriptionPlanKeys.Gold,
                SubscriptionPlanKeys.Platinum,
                SubscriptionPlanKeys.PlatinumTrial
            ],
            endDate
        );
    }


    public async Task ApplyDiscountOnSpecificRequest(
        Request request,
        Bill bill,
        decimal value,
        bool isPercentage)
    {
        var discount = await CreateDiscountAsync
        (
            GuidGenerator.Create(),
            "on specific request",
            TargetType.OnTotal,
            value,
            isPercentage,
            Clock.Now,
            [new DiscountCriteriaViewModel(DiscountSpecificationKey.RequestId,request.Id.ToString())],
            null,
            Clock.Now.AddDays(7)
        );

        if (request is not IBillableRequest billableRequest ||
            billableRequest.BillId is null ||
            billableRequest.BillId.Value != bill.Id)
        {
            throw new BusinessException(GoTrackDomainErrorCodes.CannotApplyDiscountToBill);
        }

        bill.ApplyDiscount(new AppliedDiscount(
            discount.Id,
            discount.Value,
            discount.IsPercentage,
            discount.CalculateDiscountAmount(bill.BillableAmount),
            Clock.Now,
            discount.TargetType,
            null
        ));
    }

    public async Task IncrementPromoCodeUsageForPaidBillAsync(Bill bill)
    {
        if (bill.Status != BillStatus.Paid)
        {
            return;
        }

        var promoCodes = await ExtractPromoCodesFromDiscountsAsync(bill.AppliedDiscounts.ToList());

        foreach (var code in promoCodes)
        {
            var promoCode = await PromoCodeRepository.FirstOrDefaultAsync(p => p.Code == code);

            if (promoCode == null)
            {
                Logger.LogWarning($"Attempted to increment usage for non-existent promo code: {code}");
                continue;
            }

            promoCode.IncrementUsage();
            await PromoCodeRepository.UpdateAsync(promoCode);
        }
    }

}