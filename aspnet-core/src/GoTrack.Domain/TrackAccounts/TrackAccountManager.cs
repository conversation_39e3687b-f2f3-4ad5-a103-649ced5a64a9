using System;
using System.Threading.Tasks;
using GoTrack.AlertDefinitions;
using GoTrack.UserTrackAccountAssociations;
using Volo.Abp.Domain.Services;

namespace GoTrack.TrackAccounts;

public class TrackAccountManager : DomainService
{
    private UserTrackAccountAssociationManager UserTrackAccountAssociationManager
        => LazyServiceProvider.LazyGetRequiredService<UserTrackAccountAssociationManager>();  
    private AlertDefinitionManager AlertDefinitionManager
        => LazyServiceProvider.LazyGetRequiredService<AlertDefinitionManager>();
    public async Task DeactivateTrackAccountAsync(Guid trackAccountId)
    {
        await UserTrackAccountAssociationManager.DeactivateObserverOfTrackAccountAsync(trackAccountId);
        await AlertDefinitionManager.DeactivateAllAlertsOfTrackAccountAsync(trackAccountId);
    }
}