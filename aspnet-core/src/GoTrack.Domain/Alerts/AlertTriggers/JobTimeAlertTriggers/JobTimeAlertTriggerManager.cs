using GoTrack.AlertDefinitions.AlertDefinitionAssociations;
using GoTrack.AlertDefinitions;
using GoTrack.VehicleDeviceEventLogs;
using MassTransit;
using Microsoft.Extensions.Localization;
using System;
using System.Threading.Tasks;
using Volo.Abp.Domain.Repositories;
using GoTrack.Alerts.Contracts.Exchanges.Enums;
using GoTrack.Alerts.Contracts.Exchanges.IAlertCrud;
using System.Linq;
using GoTrack.AlertDefinitions.JobTimeAlertDefinitions;
using GoTrack.Vehicles;
using System.Collections.Generic;

namespace GoTrack.Alerts.AlertTriggers.JobTimeAlertTriggers;

public class JobTimeAlertTriggerManager : AlertTriggerManager
{
    private readonly IRepository<JobTimeAlertTrigger, Guid> _jobTimeAlertTriggerRepository;

    public JobTimeAlertTriggerManager(
        IRepository<AlertTrigger, Guid> alertTriggerRepository,
        IRepository<AlertDefinitionAssociation, Guid> alertDefinitionAssociationRepository,
        IRepository<VehicleDeviceEventLog, Guid> vehicleDeviceEventLogRepository,
        IRepository<AlertDefinition, Guid> alertDefinitionRepository,
        IPublishEndpoint publishEndpoint,
        IStringLocalizerFactory stringLocalizerFactory,
        IRepository<JobTimeAlertTrigger, Guid> jobTimeAlertTriggerRepository)
        : base(
            alertTriggerRepository,
            alertDefinitionAssociationRepository,
            vehicleDeviceEventLogRepository,
            alertDefinitionRepository,
            publishEndpoint,
            stringLocalizerFactory
        )
    {
        _jobTimeAlertTriggerRepository = jobTimeAlertTriggerRepository;
    }

    public override async Task<LocalizedString> CreateNotificationMessageAsync(Guid alertTriggerId, DateTime notificationDateTime)
    {
        var query = await _jobTimeAlertTriggerRepository.WithDetailsAsync(x => x.Vehicle);

        var jobTimeAlertTrigger = await AsyncExecuter.SingleAsync(query.Where(x => x.Id == alertTriggerId));

        var message = Localizer[
            "GoTrack:JobTimeMessage",
            jobTimeAlertTrigger.Vehicle.LicensePlate.Serial,
            notificationDateTime.ToString("yyyy-MM-dd HH:mm:ss tt")
        ];

        return message;
    }


    public override List<AlertTrigger> GenerateAlertTriggers(AlertDefinition alertDefinition, ICollection<Vehicle> uniqueVehicles)
    {
        var jobTimeAlertDefinition = alertDefinition as JobTimeAlertDefinition;

        return uniqueVehicles.Select(vehicle =>
           new JobTimeAlertTrigger(
                GuidGenerator.Create(),
                [.. jobTimeAlertDefinition!.NotificationMethods],
                jobTimeAlertDefinition.TrackAccountId,
                jobTimeAlertDefinition.Id,
                vehicle.Id,
                jobTimeAlertDefinition.Name,
                jobTimeAlertDefinition.StartTime,
                jobTimeAlertDefinition.EndTime,
                [.. jobTimeAlertDefinition.DaysOfWeek]
            )
        ).ToList<AlertTrigger>();
    }

    public override async Task<AlertCrud> GenerateAlertCrudAsync(AlertTrigger trigger, CrudType crudType)
    {
        var jobTimeAlertTrigger = trigger as JobTimeAlertTrigger;

        var lastVehicleDeviceEventLog = (await VehicleDeviceEventLogRepository.WithDetailsAsync(x => x.Device))
                .OrderByDescending(e => e.CreationTime)
                .FirstOrDefault(log => log.VehicleId == jobTimeAlertTrigger!.VehicleId && log.EventName == EventName.Installed);

        return new JobTimeAlertCrud()
        {
            Id = jobTimeAlertTrigger!.Id,
            Code = AlertCode.JobTime,
            AffectiveFrom = Clock.Now,
            CrudType = crudType,
            Imei = lastVehicleDeviceEventLog?.Device.Imei,
            Name = jobTimeAlertTrigger.Name,
            StartTime = jobTimeAlertTrigger.StartTime,
            EndTime = jobTimeAlertTrigger.EndTime,
            DaysOfWeek = [.. jobTimeAlertTrigger.DaysOfWeek]
        };
    }
}
