using GoTrack.AlertDefinitions;
using GoTrack.AlertDefinitions.AlertDefinitionAssociations;
using GoTrack.AlertDefinitions.ExceedingSpeedAlertDefinitions;
using GoTrack.Alerts.Contracts.Exchanges.Enums;
using GoTrack.Alerts.Contracts.Exchanges.IAlertCrud;
using GoTrack.VehicleDeviceEventLogs;
using GoTrack.Vehicles;
using MassTransit;
using Microsoft.Extensions.Localization;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Volo.Abp.Domain.Repositories;

namespace GoTrack.Alerts.AlertTriggers.ExceedingSpeedAlertTriggers;

public class ExceedingSpeedAlertTriggerManager : AlertTriggerManager
{
    private readonly IRepository<ExceedingSpeedAlertTrigger, Guid> _exceedingSpeedAlertTriggerRepository;

    public ExceedingSpeedAlertTriggerManager(
        IRepository<AlertTrigger, Guid> alertTriggerRepository,
        IRepository<AlertDefinitionAssociation, Guid> alertDefinitionAssociationRepository,
        IRepository<VehicleDeviceEventLog, Guid> vehicleDeviceEventLogRepository,
        IRepository<AlertDefinition, Guid> alertDefinitionRepository,
        IPublishEndpoint publishEndpoint,
        IStringLocalizerFactory stringLocalizerFactory,
        IRepository<ExceedingSpeedAlertTrigger, Guid> exceedingSpeedAlertTriggerRepository)
        : base(
            alertTriggerRepository,
            alertDefinitionAssociationRepository,
            vehicleDeviceEventLogRepository,
            alertDefinitionRepository,
            publishEndpoint,
            stringLocalizerFactory
        )
    {
        _exceedingSpeedAlertTriggerRepository = exceedingSpeedAlertTriggerRepository;
    }

    public override async Task<LocalizedString> CreateNotificationMessageAsync(Guid alertTriggerId, DateTime notificationDateTime)
    {
        var query =
            await _exceedingSpeedAlertTriggerRepository.WithDetailsAsync(x => x.Vehicle);
        var exceedingSpeedAlertTrigger = await AsyncExecuter.SingleAsync(query.Where(x => x.Id == alertTriggerId));

        var message = Localizer[
            "GoTrack:OverSpeedMessage",
            exceedingSpeedAlertTrigger.Vehicle.LicensePlate.Serial,
            exceedingSpeedAlertTrigger.MaxSpeed.ToString("0"),
            notificationDateTime.ToString("yyyy-MM-dd HH:mm:ss tt")
        ];

        return message;
    }

    public override List<AlertTrigger> GenerateAlertTriggers(AlertDefinition alertDefinition, ICollection<Vehicle> uniqueVehicles)
    {
        var exceedingSpeedAlertDefinition = alertDefinition as ExceedingSpeedAlertDefinition;

        return uniqueVehicles.Select(vehicle =>
            new ExceedingSpeedAlertTrigger(
                GuidGenerator.Create(),
                [.. exceedingSpeedAlertDefinition!.NotificationMethods],
                exceedingSpeedAlertDefinition.TrackAccountId,
                exceedingSpeedAlertDefinition.Id,
                vehicle.Id,
                exceedingSpeedAlertDefinition.MaxSpeed
            )
        ).ToList<AlertTrigger>();
    }

    public override async Task<AlertCrud> GenerateAlertCrudAsync(AlertTrigger trigger, CrudType crudType)
    {
        var exceedingSpeedAlertTrigger = trigger as ExceedingSpeedAlertTrigger;

        var lastVehicleDeviceEventLog = (await VehicleDeviceEventLogRepository.WithDetailsAsync(x => x.Device))
                .OrderByDescending(e => e.CreationTime)
                .FirstOrDefault(log => log.VehicleId == exceedingSpeedAlertTrigger!.VehicleId && log.EventName == EventName.Installed);

        return new OverSpeedAlertCrud()
        {
            Id = exceedingSpeedAlertTrigger!.Id,
            Code = AlertCode.OverSpeed,
            AffectiveFrom = Clock.Now,
            CrudType = crudType,
            SpeedLimit = (int)exceedingSpeedAlertTrigger.MaxSpeed,
            Imei = lastVehicleDeviceEventLog?.Device.Imei
        };
    }
}
