using GoTrack.Identity;
using GoTrack.Msisdns;
using GoTrack.Observations;
using GoTrack.TrackableEntities;
using GoTrack.TrackAccounts;
using GoTrack.Vehicles;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using GoTrack.TrackAccounts.TrackAccountSubscriptions;
using Volo.Abp;
using Volo.Abp.Data;
using Volo.Abp.DistributedLocking;
using Volo.Abp.Domain.Repositories;
using Volo.Abp.Domain.Services;
using Volo.Abp.Identity;

namespace GoTrack.UserTrackAccountAssociations;

public class UserTrackAccountAssociationManager : DomainService, IUserTrackAccountAssociationManager
{
    private readonly IIdentityUserRepository _userRepository;
    private readonly IRepository<UserTrackAccountAssociation, Guid> _userTrackAccountAssociationRepository;

    protected IRepository<VehicleGroupVehicle, Guid> VehicleGroupVehicleRepository =>
        LazyServiceProvider.LazyGetRequiredService<IRepository<VehicleGroupVehicle, Guid>>();

    protected IRepository<Vehicle, Guid> VehicleRepository =>
        LazyServiceProvider.LazyGetRequiredService<IRepository<Vehicle, Guid>>();

    protected IMsisdnManager MsisdnManager =>
        LazyServiceProvider.LazyGetRequiredService<IMsisdnManager>();

    protected IDataFilter DataFilter => LazyServiceProvider.LazyGetRequiredService<IDataFilter>();

    private TrackAccountSubscriptionManager AccountSubscriptionManager
        => LazyServiceProvider.LazyGetRequiredService<TrackAccountSubscriptionManager>();

    private IAbpDistributedLock DistributedLock
        => LazyServiceProvider.LazyGetRequiredService<IAbpDistributedLock>();

    public UserTrackAccountAssociationManager(
        IIdentityUserRepository userRepository,
        IRepository<UserTrackAccountAssociation, Guid> userTrackAccountAssociationRepository)
    {
        _userRepository = userRepository;
        _userTrackAccountAssociationRepository = userTrackAccountAssociationRepository;
    }

    public async Task<UserTrackAccountAssociation> CreateOwnerAsync(Guid trackAccountId, Guid ownerId)
    {
        using var _ = DataFilter.Disable<IHostTenantUserFilter>();

        var user = await _userRepository.GetAsync(ownerId);

        var msisdn = MsisdnManager.Create(user.PhoneNumber);

        var userTrackAccountAssociation = new UserTrackAccountAssociation(
            id: GuidGenerator.Create(),
            trackAccountId: trackAccountId,
            associationType: AssociationType.Owner,
            status: UserTrackAccountAssociationStatus.Active,
            phoneNumber: msisdn,
            userId: ownerId,
            name: user.Name
        );

        await _userTrackAccountAssociationRepository.InsertAsync(userTrackAccountAssociation);

        return userTrackAccountAssociation;
    }

    public async Task<UserTrackAccountAssociation> CreateObserverAsync(Guid trackAccountId, Msisdn phoneNumber,
        string name)
    {
        var userTrackAccountAssociation = new UserTrackAccountAssociation(
            id: GuidGenerator.Create(),
            trackAccountId: trackAccountId,
            associationType: AssociationType.Observer,
            status: UserTrackAccountAssociationStatus.Active,
            phoneNumber: phoneNumber,
            name: name
        );

        var user = await _userRepository.FindByNormalizedUserNameAsync(phoneNumber.ToString());
        if (user is not null)
            userTrackAccountAssociation.SetUserId(user.Id);

        await _userTrackAccountAssociationRepository.InsertAsync(userTrackAccountAssociation);

        return userTrackAccountAssociation;
    }

    public async Task<UserTrackAccountAssociation> UpdateAsync(Guid userTrackAccountAssociationId, string name,
        Msisdn phoneNumber)
    {
        var userTrackAccountAssociation = await _userTrackAccountAssociationRepository.GetAsync(x =>
            x.Id == userTrackAccountAssociationId
        );

        var user = await _userRepository.FindByNormalizedUserNameAsync(phoneNumber.ToString());

        userTrackAccountAssociation.UpdateNameAndPhoneNumberAndUser(name, phoneNumber, user?.Id);

        await _userTrackAccountAssociationRepository.UpdateAsync(userTrackAccountAssociation);

        return userTrackAccountAssociation;
    }

    public async Task DeleteAssociationAsync(Msisdn phoneNumber, Guid trackAccountId)
    {
        var userTrackAccountAssociation = await _userTrackAccountAssociationRepository.GetAsync(x =>
            x.TrackAccountId == trackAccountId &&
            x.PhoneNumber == phoneNumber
        );

        await _userTrackAccountAssociationRepository.DeleteAsync(userTrackAccountAssociation);
    }

    public async Task ActivateUserAsync(Guid userId, Guid trackAccountId)
    {
        await using var handle =
            await DistributedLock.TryAcquireAsync($"ActivateUserForTrackAccountId:{trackAccountId}",
                TimeSpan.FromSeconds(5));

        if (handle is null)
        {
            throw new BusinessException(GoTrackDomainErrorCodes.CurrencyException);
        }

        var userTrackAccountAssociation = await _userTrackAccountAssociationRepository.GetAsync(x =>
            x.UserId == userId &&
            x.TrackAccountId == trackAccountId &&
            x.AssociationType == AssociationType.Observer
        );


        var activeUserCount =
            await _userTrackAccountAssociationRepository.CountAsync(x =>
                x.Status == UserTrackAccountAssociationStatus.Active &&
                x.TrackAccountId == trackAccountId &&
                x.AssociationType == AssociationType.Observer);

        var subscription =
            await AccountSubscriptionManager.GetCurrentActiveTrackAccountSubscriptionAsync(trackAccountId);

        if (subscription.UserCount == activeUserCount)
        {
            throw new BusinessException(GoTrackDomainErrorCodes.ObserverLimitReached)
                .WithData("MaxObservers", subscription.UserCount);
        }

        userTrackAccountAssociation.ActivateUser();

        await _userTrackAccountAssociationRepository.UpdateAsync(userTrackAccountAssociation);
    }

    public async Task DeactivateUserAsync(Guid userId, Guid trackAccountId)
    {
        var userTrackAccountAssociation = await _userTrackAccountAssociationRepository.GetAsync(x =>
            x.UserId == userId &&
            x.TrackAccountId == trackAccountId &&
            x.AssociationType == AssociationType.Observer
        );

        userTrackAccountAssociation.DeactivateUser();

        await _userTrackAccountAssociationRepository.UpdateAsync(userTrackAccountAssociation);
    }

    public async Task DeactivateUserAsync(Guid id)
    {
        var userTrackAccountAssociation = await _userTrackAccountAssociationRepository.GetAsync(x =>
            x.Id == id &&
            x.AssociationType == AssociationType.Observer
        );

        userTrackAccountAssociation.DeactivateUser();

        await _userTrackAccountAssociationRepository.UpdateAsync(userTrackAccountAssociation);
    }

    public async Task<IQueryable<UserTrackAccountAssociation>> GetQueryableOfUsersThatRelatesToVehicleAsync(
        Guid vehicleId)
    {
        using var _ = DataFilter.Disable<IHostTenantUserFilter>();
        using var __ = DataFilter.Disable<ICustomerUserFilter>();
        using var ___ = DataFilter.Disable<IHaveTrackAccount>();

        var vehicle = await VehicleRepository.GetAsync(vehicleId);

        var vehicleGroupVehicleQuery = await VehicleGroupVehicleRepository.GetQueryableAsync();
        vehicleGroupVehicleQuery = vehicleGroupVehicleQuery.Where(x => x.VehicleId == vehicleId);

        var vehicleGroupsIdOfVehilce =
            await AsyncExecuter.ToListAsync(vehicleGroupVehicleQuery.Select(x => x.VehicleGroupId));

        var userTrackAccountAssociationsQuery =
            await _userTrackAccountAssociationRepository.WithDetailsAsync(x => x.Observations);

        return userTrackAccountAssociationsQuery.Where(x =>
            x.Status == UserTrackAccountAssociationStatus.Active &&
            x.TrackAccountId == vehicle.TrackAccountId &&
            x.AssociationType == AssociationType.Owner ||
            (
                x.AssociationType == AssociationType.Observer &&
                (
                    x.Observations.Where(x => x.ObservationType == ObservationType.ObserverVehicle)
                        .Cast<ObservationVehicle>()
                        .Any(x => x.VehicleId == vehicleId) ||
                    x.Observations.Where(x => x.ObservationType == ObservationType.ObserverVehicleGroup)
                        .Cast<ObservationVehicleGroup>()
                        .Any(x => vehicleGroupsIdOfVehilce.Contains(x.VehicleGroupId))
                )
            )
        );
    }

    public async Task UpdateAssociationsForUserAsync(Guid userId, string name, Msisdn phoneNumber)
    {
        var query = await _userTrackAccountAssociationRepository.GetQueryableAsync();
        var associations = await AsyncExecuter.ToListAsync(query.Where(x => x.UserId == userId));

        foreach (var association in associations)
        {
            association.UpdateNameAndPhoneNumberAndUser(name, phoneNumber, userId);
        }

        if (associations.Any())
        {
            await _userTrackAccountAssociationRepository.UpdateManyAsync(associations);
        }
    }

    public async Task<List<Guid>> GetTrackAccountIdsAssociatedWithUserAsOwnerAsync(Guid userId)
    {
        var query = await _userTrackAccountAssociationRepository.GetQueryableAsync();
        
        query = query.Where(association =>
                association.UserId == userId
                && association.AssociationType == AssociationType.Owner
                );

        return await AsyncExecuter.ToListAsync(query.Select(association => association.TrackAccountId));
    }

    public async Task DeactivateObserverOfTrackAccountAsync(Guid trackAccountId)
    {
        var query = await _userTrackAccountAssociationRepository.GetQueryableAsync();
        
        query = query.Where(association =>
            association.TrackAccountId == trackAccountId
            && association.AssociationType == AssociationType.Observer
        );

        var accountAssociationsToDeactivate = await AsyncExecuter.ToListAsync(query);

        foreach (var association in accountAssociationsToDeactivate)
        {
            association.DeactivateUser();
        }

        await _userTrackAccountAssociationRepository.UpdateManyAsync(accountAssociationsToDeactivate);
    }
}