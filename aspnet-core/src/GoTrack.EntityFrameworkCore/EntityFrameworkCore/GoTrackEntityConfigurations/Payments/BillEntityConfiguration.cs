using GoTrack.Payments.Bills;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Volo.Abp.EntityFrameworkCore.Modeling;

namespace GoTrack.EntityFrameworkCore.GoTrackEntityConfigurations.Payments;

public class BillEntityConfiguration : IEntityTypeConfiguration<Bill>
{
    public void Configure(EntityTypeBuilder<Bill> builder)
    {
        builder.ConfigureByConvention();
        builder.ToGoTrackTable();
        
        builder.OwnsMany(x => x.AppliedDiscounts);
        builder.OwnsMany(x => x.BillLineItems);
    }
}