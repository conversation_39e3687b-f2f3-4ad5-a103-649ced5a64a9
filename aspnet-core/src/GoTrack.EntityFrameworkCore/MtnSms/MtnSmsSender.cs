using System;
using System.Linq;
using System.Net.Http;
using System.Text.RegularExpressions;
using System.Threading.Tasks;
using GoTrack.MtnSms.Options;
using Volo.Abp.DependencyInjection;
using Volo.Abp.Settings;
using Volo.Abp.Sms;

namespace GoTrack.MtnSms;

public partial class MtnSmsSender : ISmsSender, ITransientDependency
{
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly ISettingProvider _settingProvider;

    public MtnSmsSender(IHttpClientFactory httpClientFactory, ISettingProvider settingProvider)
    {
        _httpClientFactory = httpClientFactory;
        _settingProvider = settingProvider;
    }

    public async Task SendAsync(SmsMessage smsMessage)
    {
        if (await _settingProvider.IsMtnSmsSenderSettingExist() is false)
            throw new Exception("MtnSmsSender Not Configured"); 

        var senderId = await _settingProvider.GetSenderIdOfMtnSmsSender();
        var username = await _settingProvider.GetUsernameOfMtnSmsSender();
        var password = await _settingProvider.GetPasswordOfMtnSmsSender();

        const string apiUrl = "https://services.mtnsyr.com:7443/general/MTNSERVICES/ConcatenatedSender.aspx";

        var language = DetectLanguage(smsMessage.Text);

        var phoneNumberWith12digits = ConvertTo12digits(smsMessage.PhoneNumber);

        var encodedMessage = ConvertToUnicodeHex(smsMessage.Text);
        
        var queryString = $"?User={username}&Pass={password}" +
                          $"&From={senderId}" +
                          $"&Gsm={string.Join(";", phoneNumberWith12digits)}" +
                          $"&Lang={language}" +
                          $"&Msg={Uri.EscapeDataString(encodedMessage)}";


        var client = _httpClientFactory.CreateClient();

        var response = await client.GetAsync($"{apiUrl}{queryString}");

        if (!response.IsSuccessStatusCode)
        {
            throw new Exception($"SMS sending failed: {response.StatusCode} - {response.ReasonPhrase}");
        }
    }

    private static string ConvertTo12digits(string smsMessagePhoneNumber)
    {
        if (string.IsNullOrEmpty(smsMessagePhoneNumber))
            throw new NotImplementedException();

        if (smsMessagePhoneNumber.Length == 12 && smsMessagePhoneNumber.StartsWith("9639"))
            return smsMessagePhoneNumber;

        if (smsMessagePhoneNumber.Length == 14 && smsMessagePhoneNumber.StartsWith("009639"))
        {
            return smsMessagePhoneNumber[2..];
        }
        
        throw new NotImplementedException();
    }

    [GeneratedRegex(@"\p{IsArabic}")]
    private static partial Regex ArabicRegex();

    private static int DetectLanguage(string message)
    {
        return ArabicRegex().IsMatch(message) ? 0 : 1;
    }

    private static string ConvertToUnicodeHex(string input)
    {
        return string.Concat(input.Select(c => ((int)c).ToString("X4")));
    }
}