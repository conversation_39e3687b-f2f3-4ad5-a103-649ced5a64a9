using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace GoTrack.Migrations
{
    /// <inheritdoc />
    public partial class AddBillableAmount : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<decimal>(
                name: "BillableAmount",
                table: "GoTrackBills",
                type: "decimal(65,30)",
                nullable: false,
                defaultValue: 0m);

            migrationBuilder.AddColumn<decimal>(
                name: "BillableAmount",
                table: "BillLineItem",
                type: "decimal(65,30)",
                nullable: false,
                defaultValue: 0m);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "BillableAmount",
                table: "GoTrackBills");

            migrationBuilder.DropColumn(
                name: "BillableAmount",
                table: "BillLineItem");
        }
    }
}
