// <auto-generated />
using System;
using GoTrack.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using NetTopologySuite.Geometries;
using Volo.Abp.EntityFrameworkCore;

#nullable disable

namespace GoTrack.Migrations
{
    [DbContext(typeof(GoTrackDbContext))]
    partial class GoTrackDbContextModelSnapshot : ModelSnapshot
    {
        protected override void BuildModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("_Abp_DatabaseProvider", EfCoreDatabaseProvider.MySql)
                .HasAnnotation("ProductVersion", "8.0.4")
                .HasAnnotation("Relational:MaxIdentifierLength", 64);

            MySqlModelBuilderExtensions.AutoIncrementColumns(modelBuilder);

            modelBuilder.Entity("GoTrack.AlertDefinitions.AlertDefinition", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("char(36)");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .HasMaxLength(40)
                        .HasColumnType("varchar(40)")
                        .HasColumnName("ConcurrencyStamp");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("CreationTime");

                    b.Property<Guid?>("CreatorId")
                        .HasColumnType("char(36)")
                        .HasColumnName("CreatorId");

                    b.Property<Guid?>("DeleterId")
                        .HasColumnType("char(36)")
                        .HasColumnName("DeleterId");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("DeletionTime");

                    b.Property<string>("ExtraProperties")
                        .IsRequired()
                        .HasColumnType("longtext")
                        .HasColumnName("ExtraProperties");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint(1)")
                        .HasDefaultValue(false)
                        .HasColumnName("IsDeleted");

                    b.Property<bool>("IsEnabled")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("LastModificationTime");

                    b.Property<Guid?>("LastModifierId")
                        .HasColumnType("char(36)")
                        .HasColumnName("LastModifierId");

                    b.Property<string>("NotificationMethods")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<Guid>("TrackAccountId")
                        .HasColumnType("char(36)");

                    b.Property<int>("Type")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.ToTable("GoTrackAlertDefinitions", (string)null);

                    b.UseTptMappingStrategy();
                });

            modelBuilder.Entity("GoTrack.AlertDefinitions.AlertDefinitionAssociations.AlertDefinitionAssociation", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("char(36)");

                    b.Property<Guid>("AlertDefinitionId")
                        .HasColumnType("char(36)");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .HasMaxLength(40)
                        .HasColumnType("varchar(40)")
                        .HasColumnName("ConcurrencyStamp");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("CreationTime");

                    b.Property<Guid?>("CreatorId")
                        .HasColumnType("char(36)")
                        .HasColumnName("CreatorId");

                    b.Property<Guid?>("DeleterId")
                        .HasColumnType("char(36)")
                        .HasColumnName("DeleterId");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("DeletionTime");

                    b.Property<string>("ExtraProperties")
                        .IsRequired()
                        .HasColumnType("longtext")
                        .HasColumnName("ExtraProperties");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint(1)")
                        .HasDefaultValue(false)
                        .HasColumnName("IsDeleted");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("LastModificationTime");

                    b.Property<Guid?>("LastModifierId")
                        .HasColumnType("char(36)")
                        .HasColumnName("LastModifierId");

                    b.Property<Guid>("TrackableEntityAssociationId")
                        .HasColumnType("char(36)");

                    b.HasKey("Id");

                    b.HasIndex("TrackableEntityAssociationId");

                    b.ToTable("AlertDefinitionAssociations");
                });

            modelBuilder.Entity("GoTrack.AlertDefinitions.RouteAlertDefinitions.RouteAlertRoutes.RouteAlertRoute", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("char(36)");

                    b.Property<Guid>("AlertDefinitionId")
                        .HasColumnType("char(36)");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .HasMaxLength(40)
                        .HasColumnType("varchar(40)")
                        .HasColumnName("ConcurrencyStamp");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("CreationTime");

                    b.Property<Guid?>("CreatorId")
                        .HasColumnType("char(36)")
                        .HasColumnName("CreatorId");

                    b.Property<Guid?>("DeleterId")
                        .HasColumnType("char(36)")
                        .HasColumnName("DeleterId");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("DeletionTime");

                    b.Property<Guid?>("ExitingRouteAlertDefinitionId")
                        .HasColumnType("char(36)");

                    b.Property<string>("ExtraProperties")
                        .IsRequired()
                        .HasColumnType("longtext")
                        .HasColumnName("ExtraProperties");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint(1)")
                        .HasDefaultValue(false)
                        .HasColumnName("IsDeleted");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("LastModificationTime");

                    b.Property<Guid?>("LastModifierId")
                        .HasColumnType("char(36)")
                        .HasColumnName("LastModifierId");

                    b.Property<Guid>("RouteId")
                        .HasColumnType("char(36)");

                    b.HasKey("Id");

                    b.HasIndex("ExitingRouteAlertDefinitionId");

                    b.HasIndex("RouteId");

                    b.ToTable("GoTrackRouteAlertRoutes", (string)null);
                });

            modelBuilder.Entity("GoTrack.AlertDefinitions.ZoneAlertDefinitions.ZoneAlertGeoZones.ZoneAlertGeoZone", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("char(36)");

                    b.Property<Guid>("AlertDefinitionId")
                        .HasColumnType("char(36)");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .HasMaxLength(40)
                        .HasColumnType("varchar(40)")
                        .HasColumnName("ConcurrencyStamp");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("CreationTime");

                    b.Property<Guid?>("CreatorId")
                        .HasColumnType("char(36)")
                        .HasColumnName("CreatorId");

                    b.Property<Guid?>("DeleterId")
                        .HasColumnType("char(36)")
                        .HasColumnName("DeleterId");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("DeletionTime");

                    b.Property<Guid?>("EnteringZoneAlertDefinitionId")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("ExitingZoneAlertDefinitionId")
                        .HasColumnType("char(36)");

                    b.Property<string>("ExtraProperties")
                        .IsRequired()
                        .HasColumnType("longtext")
                        .HasColumnName("ExtraProperties");

                    b.Property<Guid>("GeoZoneId")
                        .HasColumnType("char(36)");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint(1)")
                        .HasDefaultValue(false)
                        .HasColumnName("IsDeleted");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("LastModificationTime");

                    b.Property<Guid?>("LastModifierId")
                        .HasColumnType("char(36)")
                        .HasColumnName("LastModifierId");

                    b.HasKey("Id");

                    b.HasIndex("EnteringZoneAlertDefinitionId");

                    b.HasIndex("ExitingZoneAlertDefinitionId");

                    b.HasIndex("GeoZoneId");

                    b.ToTable("GoTrackZoneAlertGeoZones", (string)null);
                });

            modelBuilder.Entity("GoTrack.Alerts.AlertLogs.AlertLog", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("char(36)");

                    b.Property<Guid>("AlertTriggerId")
                        .HasColumnType("char(36)");

                    b.Property<int>("AlertType")
                        .HasColumnType("int");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .HasMaxLength(40)
                        .HasColumnType("varchar(40)")
                        .HasColumnName("ConcurrencyStamp");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("CreationTime");

                    b.Property<Guid?>("CreatorId")
                        .HasColumnType("char(36)")
                        .HasColumnName("CreatorId");

                    b.Property<Guid?>("DeleterId")
                        .HasColumnType("char(36)")
                        .HasColumnName("DeleterId");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("DeletionTime");

                    b.Property<Guid>("DeviceId")
                        .HasColumnType("char(36)");

                    b.Property<DateTime?>("EndedAt")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("ExtraProperties")
                        .IsRequired()
                        .HasColumnType("longtext")
                        .HasColumnName("ExtraProperties");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint(1)")
                        .HasDefaultValue(false)
                        .HasColumnName("IsDeleted");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("LastModificationTime");

                    b.Property<Guid?>("LastModifierId")
                        .HasColumnType("char(36)")
                        .HasColumnName("LastModifierId");

                    b.Property<DateTime>("StartedAt")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid>("TrackAccountId")
                        .HasColumnType("char(36)");

                    b.Property<Guid>("VehicleId")
                        .HasColumnType("char(36)");

                    b.HasKey("Id");

                    b.HasIndex("AlertTriggerId");

                    b.HasIndex("DeviceId");

                    b.HasIndex("VehicleId");

                    b.ToTable("GoTrackAlertLogs", (string)null);
                });

            modelBuilder.Entity("GoTrack.Alerts.AlertTriggers.AlertTrigger", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("char(36)");

                    b.Property<Guid>("AlertDefinitionId")
                        .HasColumnType("char(36)");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .HasMaxLength(40)
                        .HasColumnType("varchar(40)")
                        .HasColumnName("ConcurrencyStamp");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("CreationTime");

                    b.Property<Guid?>("CreatorId")
                        .HasColumnType("char(36)")
                        .HasColumnName("CreatorId");

                    b.Property<Guid?>("DeleterId")
                        .HasColumnType("char(36)")
                        .HasColumnName("DeleterId");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("DeletionTime");

                    b.Property<string>("ExtraProperties")
                        .IsRequired()
                        .HasColumnType("longtext")
                        .HasColumnName("ExtraProperties");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint(1)")
                        .HasDefaultValue(false)
                        .HasColumnName("IsDeleted");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("LastModificationTime");

                    b.Property<Guid?>("LastModifierId")
                        .HasColumnType("char(36)")
                        .HasColumnName("LastModifierId");

                    b.Property<string>("NotificationMethods")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<Guid>("TrackAccountId")
                        .HasColumnType("char(36)");

                    b.Property<int>("Type")
                        .HasColumnType("int");

                    b.Property<Guid>("VehicleId")
                        .HasColumnType("char(36)");

                    b.HasKey("Id");

                    b.HasIndex("VehicleId");

                    b.ToTable("GoTrackAlertTriggers", (string)null);

                    b.UseTptMappingStrategy();
                });

            modelBuilder.Entity("GoTrack.Devices.Device", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("char(36)");

                    b.Property<string>("Brand")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .HasMaxLength(40)
                        .HasColumnType("varchar(40)")
                        .HasColumnName("ConcurrencyStamp");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("CreationTime");

                    b.Property<Guid?>("CreatorId")
                        .HasColumnType("char(36)")
                        .HasColumnName("CreatorId");

                    b.Property<Guid?>("DeleterId")
                        .HasColumnType("char(36)")
                        .HasColumnName("DeleterId");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("DeletionTime");

                    b.Property<string>("ExtraProperties")
                        .IsRequired()
                        .HasColumnType("longtext")
                        .HasColumnName("ExtraProperties");

                    b.Property<string>("Imei")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint(1)")
                        .HasDefaultValue(false)
                        .HasColumnName("IsDeleted");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("LastModificationTime");

                    b.Property<Guid?>("LastModifierId")
                        .HasColumnType("char(36)")
                        .HasColumnName("LastModifierId");

                    b.Property<string>("Model")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<Guid?>("OwnerId")
                        .HasColumnType("char(36)");

                    b.Property<int>("Protocol")
                        .HasColumnType("int");

                    b.Property<string>("Sim")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("OwnerId");

                    b.ToTable("GoTrackDevices", (string)null);
                });

            modelBuilder.Entity("GoTrack.Devices.DeviceStatusLog", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("char(36)");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("CreationTime");

                    b.Property<Guid?>("CreatorId")
                        .HasColumnType("char(36)")
                        .HasColumnName("CreatorId");

                    b.Property<DateTime>("Date")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("DeleterId")
                        .HasColumnType("char(36)")
                        .HasColumnName("DeleterId");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("DeletionTime");

                    b.Property<Guid>("DeviceId")
                        .HasColumnType("char(36)");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint(1)")
                        .HasDefaultValue(false)
                        .HasColumnName("IsDeleted");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("LastModificationTime");

                    b.Property<Guid?>("LastModifierId")
                        .HasColumnType("char(36)")
                        .HasColumnName("LastModifierId");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("DeviceId");

                    b.ToTable("GoTrackDeviceStatusLogs", (string)null);
                });

            modelBuilder.Entity("GoTrack.FCMDevices.FcmDevice", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("char(36)");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("CreationTime");

                    b.Property<Guid?>("CreatorId")
                        .HasColumnType("char(36)")
                        .HasColumnName("CreatorId");

                    b.Property<Guid?>("DeleterId")
                        .HasColumnType("char(36)")
                        .HasColumnName("DeleterId");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("DeletionTime");

                    b.Property<string>("DeviceId")
                        .HasMaxLength(500)
                        .HasColumnType("varchar(500)");

                    b.Property<int>("DeviceType")
                        .HasColumnType("int");

                    b.Property<string>("FcmToken")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("varchar(500)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint(1)")
                        .HasDefaultValue(false)
                        .HasColumnName("IsDeleted");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("LastModificationTime");

                    b.Property<Guid?>("LastModifierId")
                        .HasColumnType("char(36)")
                        .HasColumnName("LastModifierId");

                    b.Property<string>("Name")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<Guid?>("UserId")
                        .HasColumnType("char(36)");

                    b.HasKey("Id");

                    b.HasIndex("FcmToken")
                        .IsUnique();

                    b.ToTable("GoTrackFcmDevices", (string)null);
                });

            modelBuilder.Entity("GoTrack.GeoNodes.GeoNode", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("char(36)");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .HasMaxLength(40)
                        .HasColumnType("varchar(40)")
                        .HasColumnName("ConcurrencyStamp");

                    b.Property<string>("ExtraProperties")
                        .IsRequired()
                        .HasColumnType("longtext")
                        .HasColumnName("ExtraProperties");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<int>("NodeType")
                        .HasColumnType("int");

                    b.Property<Guid?>("ParentNodeId")
                        .HasColumnType("char(36)");

                    b.Property<Guid>("TreeId")
                        .HasColumnType("char(36)");

                    b.HasKey("Id");

                    b.ToTable("GoTrackGeoNodes", (string)null);
                });

            modelBuilder.Entity("GoTrack.GeoZones.GeoZone", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("char(36)");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .HasMaxLength(40)
                        .HasColumnType("varchar(40)")
                        .HasColumnName("ConcurrencyStamp");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("CreationTime");

                    b.Property<Guid?>("CreatorId")
                        .HasColumnType("char(36)")
                        .HasColumnName("CreatorId");

                    b.Property<Guid?>("DeleterId")
                        .HasColumnType("char(36)")
                        .HasColumnName("DeleterId");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("DeletionTime");

                    b.Property<string>("ExtraProperties")
                        .IsRequired()
                        .HasColumnType("longtext")
                        .HasColumnName("ExtraProperties");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint(1)")
                        .HasDefaultValue(false)
                        .HasColumnName("IsDeleted");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("LastModificationTime");

                    b.Property<Guid?>("LastModifierId")
                        .HasColumnType("char(36)")
                        .HasColumnName("LastModifierId");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<Guid>("TrackAccountId")
                        .HasColumnType("char(36)");

                    b.HasKey("Id");

                    b.HasIndex("TrackAccountId");

                    b.ToTable("GoTrackGeoZones", (string)null);
                });

            modelBuilder.Entity("GoTrack.Identity.IdentityUserProfile", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("char(36)");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .HasMaxLength(40)
                        .HasColumnType("varchar(40)")
                        .HasColumnName("ConcurrencyStamp");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("CreationTime");

                    b.Property<Guid?>("CreatorId")
                        .HasColumnType("char(36)")
                        .HasColumnName("CreatorId");

                    b.Property<Guid?>("DeleterId")
                        .HasColumnType("char(36)")
                        .HasColumnName("DeleterId");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("DeletionTime");

                    b.Property<string>("ExtraProperties")
                        .IsRequired()
                        .HasColumnType("longtext")
                        .HasColumnName("ExtraProperties");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint(1)")
                        .HasDefaultValue(false)
                        .HasColumnName("IsDeleted");

                    b.Property<double?>("LastAcceptedPolicyVersion")
                        .HasColumnType("double");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("LastModificationTime");

                    b.Property<Guid?>("LastModifierId")
                        .HasColumnType("char(36)")
                        .HasColumnName("LastModifierId");

                    b.Property<string>("PreferredLanguage")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasColumnType("longtext")
                        .HasDefaultValue("ar");

                    b.Property<Guid>("UserId")
                        .HasColumnType("char(36)");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("GoTrackIdentityUserProfiles", (string)null);
                });

            modelBuilder.Entity("GoTrack.Observations.Observation", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("char(36)");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("CreationTime");

                    b.Property<Guid?>("CreatorId")
                        .HasColumnType("char(36)")
                        .HasColumnName("CreatorId");

                    b.Property<Guid?>("DeleterId")
                        .HasColumnType("char(36)")
                        .HasColumnName("DeleterId");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("DeletionTime");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint(1)")
                        .HasDefaultValue(false)
                        .HasColumnName("IsDeleted");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("LastModificationTime");

                    b.Property<Guid?>("LastModifierId")
                        .HasColumnType("char(36)")
                        .HasColumnName("LastModifierId");

                    b.Property<int>("ObservationType")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(0);

                    b.Property<Guid>("UserTrackAccountAssociationId")
                        .HasColumnType("char(36)");

                    b.HasKey("Id");

                    b.HasIndex("UserTrackAccountAssociationId");

                    b.ToTable("GoTrackObservations", (string)null);

                    b.HasDiscriminator<int>("ObservationType");

                    b.UseTphMappingStrategy();
                });

            modelBuilder.Entity("GoTrack.Payments.Bills.Bill", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("char(36)");

                    b.Property<decimal>("BillableAmount")
                        .HasColumnType("decimal(65,30)");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .HasMaxLength(40)
                        .HasColumnType("varchar(40)")
                        .HasColumnName("ConcurrencyStamp");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("CreationTime");

                    b.Property<Guid?>("CreatorId")
                        .HasColumnType("char(36)")
                        .HasColumnName("CreatorId");

                    b.Property<string>("ExtraProperties")
                        .IsRequired()
                        .HasColumnType("longtext")
                        .HasColumnName("ExtraProperties");

                    b.Property<DateTime?>("PaymentDate")
                        .HasColumnType("datetime(6)");

                    b.Property<int>("PaymentMethod")
                        .HasColumnType("int");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<Guid>("UserId")
                        .HasColumnType("char(36)");

                    b.HasKey("Id");

                    b.ToTable("GoTrackBills", (string)null);
                });

            modelBuilder.Entity("GoTrack.Payments.Discounts.Discount", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("char(36)");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .HasMaxLength(40)
                        .HasColumnType("varchar(40)")
                        .HasColumnName("ConcurrencyStamp");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("CreationTime");

                    b.Property<Guid?>("CreatorId")
                        .HasColumnType("char(36)")
                        .HasColumnName("CreatorId");

                    b.Property<Guid?>("DeleterId")
                        .HasColumnType("char(36)")
                        .HasColumnName("DeleterId");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("DeletionTime");

                    b.Property<DateTime?>("EndDate")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("ExtraProperties")
                        .IsRequired()
                        .HasColumnType("longtext")
                        .HasColumnName("ExtraProperties");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint(1)")
                        .HasDefaultValue(false)
                        .HasColumnName("IsDeleted");

                    b.Property<bool>("IsPercentage")
                        .HasColumnType("tinyint(1)");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("LastModificationTime");

                    b.Property<Guid?>("LastModifierId")
                        .HasColumnType("char(36)")
                        .HasColumnName("LastModifierId");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<string>("PricingItemKeys")
                        .HasColumnType("longtext");

                    b.Property<DateTime>("StartDate")
                        .HasColumnType("datetime(6)");

                    b.Property<int>("TargetType")
                        .HasColumnType("int");

                    b.Property<decimal>("Value")
                        .HasColumnType("decimal(65,30)");

                    b.HasKey("Id");

                    b.ToTable("GoTrackDiscounts", (string)null);
                });

            modelBuilder.Entity("GoTrack.Payments.PricingItems.PricingItem", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("char(36)");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .HasMaxLength(40)
                        .HasColumnType("varchar(40)")
                        .HasColumnName("ConcurrencyStamp");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("CreationTime");

                    b.Property<Guid?>("CreatorId")
                        .HasColumnType("char(36)")
                        .HasColumnName("CreatorId");

                    b.Property<decimal>("CurrentPrice")
                        .HasColumnType("decimal(65,30)");

                    b.Property<Guid?>("DeleterId")
                        .HasColumnType("char(36)")
                        .HasColumnName("DeleterId");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("DeletionTime");

                    b.Property<string>("ExtraProperties")
                        .IsRequired()
                        .HasColumnType("longtext")
                        .HasColumnName("ExtraProperties");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint(1)")
                        .HasDefaultValue(false)
                        .HasColumnName("IsDeleted");

                    b.Property<string>("Key")
                        .IsRequired()
                        .HasColumnType("varchar(255)");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("LastModificationTime");

                    b.Property<Guid?>("LastModifierId")
                        .HasColumnType("char(36)")
                        .HasColumnName("LastModifierId");

                    b.HasKey("Id");

                    b.HasIndex("Key")
                        .IsUnique();

                    b.ToTable("GoTrackPricingItems", (string)null);
                });

            modelBuilder.Entity("GoTrack.Payments.PromoCodes.PromoCode", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("char(36)");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(8)
                        .HasColumnType("varchar(8)");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .HasMaxLength(40)
                        .HasColumnType("varchar(40)")
                        .HasColumnName("ConcurrencyStamp");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("CreationTime");

                    b.Property<Guid?>("CreatorId")
                        .HasColumnType("char(36)")
                        .HasColumnName("CreatorId");

                    b.Property<DateTime>("DateFrom")
                        .HasColumnType("datetime(6)");

                    b.Property<DateTime>("DateTo")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("DeleterId")
                        .HasColumnType("char(36)")
                        .HasColumnName("DeleterId");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("DeletionTime");

                    b.Property<Guid>("DiscountId")
                        .HasColumnType("char(36)");

                    b.Property<string>("ExtraProperties")
                        .IsRequired()
                        .HasColumnType("longtext")
                        .HasColumnName("ExtraProperties");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint(1)")
                        .HasDefaultValue(false)
                        .HasColumnName("IsDeleted");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("LastModificationTime");

                    b.Property<Guid?>("LastModifierId")
                        .HasColumnType("char(36)")
                        .HasColumnName("LastModifierId");

                    b.Property<int>("UsageCount")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("Code");

                    b.HasIndex("DiscountId");

                    b.ToTable("GoTrackPromoCodes", (string)null);
                });

            modelBuilder.Entity("GoTrack.Payments.UserFatoraPayment", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("char(36)");

                    b.Property<int>("Amount")
                        .HasColumnType("int");

                    b.Property<string>("CallBackUrl")
                        .HasColumnType("longtext");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .HasMaxLength(40)
                        .HasColumnType("varchar(40)")
                        .HasColumnName("ConcurrencyStamp");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("CreationTime");

                    b.Property<Guid?>("CreatorId")
                        .HasColumnType("char(36)")
                        .HasColumnName("CreatorId");

                    b.Property<Guid?>("DeleterId")
                        .HasColumnType("char(36)")
                        .HasColumnName("DeleterId");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("DeletionTime");

                    b.Property<string>("ExtraProperties")
                        .IsRequired()
                        .HasColumnType("longtext")
                        .HasColumnName("ExtraProperties");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint(1)")
                        .HasDefaultValue(false)
                        .HasColumnName("IsDeleted");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("LastModificationTime");

                    b.Property<Guid?>("LastModifierId")
                        .HasColumnType("char(36)")
                        .HasColumnName("LastModifierId");

                    b.Property<string>("Notes")
                        .HasColumnType("longtext");

                    b.Property<Guid?>("PaymentId")
                        .HasColumnType("char(36)");

                    b.Property<int>("PaymentStatus")
                        .HasColumnType("int");

                    b.Property<Guid>("RequestId")
                        .HasColumnType("char(36)");

                    b.Property<int>("RequestType")
                        .HasColumnType("int");

                    b.Property<string>("Rrn")
                        .HasColumnType("longtext");

                    b.Property<uint>("TerminalId")
                        .HasColumnType("int unsigned");

                    b.Property<string>("TriggerUrl")
                        .HasColumnType("longtext");

                    b.Property<string>("Url")
                        .HasColumnType("longtext");

                    b.Property<Guid>("UserId")
                        .HasColumnType("char(36)");

                    b.HasKey("Id");

                    b.ToTable("GoTrackUserFatoraPayments", (string)null);
                });

            modelBuilder.Entity("GoTrack.PrivacyPolicies.PrivacyPolicy", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("char(36)");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .HasMaxLength(40)
                        .HasColumnType("varchar(40)")
                        .HasColumnName("ConcurrencyStamp");

                    b.Property<string>("Content")
                        .IsRequired()
                        .HasMaxLength(8000)
                        .HasColumnType("varchar(8000)");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("CreationTime");

                    b.Property<Guid?>("CreatorId")
                        .HasColumnType("char(36)")
                        .HasColumnName("CreatorId");

                    b.Property<Guid?>("DeleterId")
                        .HasColumnType("char(36)")
                        .HasColumnName("DeleterId");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("DeletionTime");

                    b.Property<string>("ExtraProperties")
                        .IsRequired()
                        .HasColumnType("longtext")
                        .HasColumnName("ExtraProperties");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint(1)")
                        .HasDefaultValue(false)
                        .HasColumnName("IsDeleted");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("LastModificationTime");

                    b.Property<Guid?>("LastModifierId")
                        .HasColumnType("char(36)")
                        .HasColumnName("LastModifierId");

                    b.Property<double>("Version")
                        .HasColumnType("double");

                    b.HasKey("Id");

                    b.HasIndex("Version");

                    b.ToTable("GoTrackPrivacyPolicys", (string)null);
                });

            modelBuilder.Entity("GoTrack.Requests.Request", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("char(36)");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .HasMaxLength(40)
                        .HasColumnType("varchar(40)")
                        .HasColumnName("ConcurrencyStamp");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("CreationTime");

                    b.Property<Guid?>("CreatorId")
                        .HasColumnType("char(36)")
                        .HasColumnName("CreatorId");

                    b.Property<Guid?>("DeleterId")
                        .HasColumnType("char(36)")
                        .HasColumnName("DeleterId");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("DeletionTime");

                    b.Property<string>("ExtraProperties")
                        .IsRequired()
                        .HasColumnType("longtext")
                        .HasColumnName("ExtraProperties");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint(1)")
                        .HasDefaultValue(false)
                        .HasColumnName("IsDeleted");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("LastModificationTime");

                    b.Property<Guid?>("LastModifierId")
                        .HasColumnType("char(36)")
                        .HasColumnName("LastModifierId");

                    b.Property<Guid>("OwnerId")
                        .HasColumnType("char(36)");

                    b.Property<string>("RejectReason")
                        .HasColumnType("longtext");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<int>("Type")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("OwnerId");

                    b.ToTable("GoTrackRequests", (string)null);

                    b.UseTptMappingStrategy();
                });

            modelBuilder.Entity("GoTrack.Requests.RequestNote", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("char(36)");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("CreationTime");

                    b.Property<Guid?>("CreatorId")
                        .HasColumnType("char(36)")
                        .HasColumnName("CreatorId");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("LastModificationTime");

                    b.Property<Guid?>("LastModifierId")
                        .HasColumnType("char(36)")
                        .HasColumnName("LastModifierId");

                    b.Property<string>("Note")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<Guid>("RequestId")
                        .HasColumnType("char(36)");

                    b.HasKey("Id");

                    b.HasIndex("RequestId");

                    b.ToTable("GoTrackRequestNotes", (string)null);
                });

            modelBuilder.Entity("GoTrack.Routes.Route", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("char(36)");

                    b.Property<int>("Color")
                        .HasColumnType("int");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .HasMaxLength(40)
                        .HasColumnType("varchar(40)")
                        .HasColumnName("ConcurrencyStamp");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("CreationTime");

                    b.Property<Guid?>("CreatorId")
                        .HasColumnType("char(36)")
                        .HasColumnName("CreatorId");

                    b.Property<Guid?>("DeleterId")
                        .HasColumnType("char(36)")
                        .HasColumnName("DeleterId");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("DeletionTime");

                    b.Property<Point>("EndPoint")
                        .IsRequired()
                        .HasColumnType("point");

                    b.Property<string>("ExtraProperties")
                        .IsRequired()
                        .HasColumnType("longtext")
                        .HasColumnName("ExtraProperties");

                    b.Property<string>("HhCode")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint(1)")
                        .HasDefaultValue(false)
                        .HasColumnName("IsDeleted");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("LastModificationTime");

                    b.Property<Guid?>("LastModifierId")
                        .HasColumnType("char(36)")
                        .HasColumnName("LastModifierId");

                    b.Property<LineString>("Line")
                        .IsRequired()
                        .HasColumnType("linestring");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<Point>("StartPoint")
                        .IsRequired()
                        .HasColumnType("point");

                    b.Property<Guid>("TrackAccountId")
                        .HasColumnType("char(36)");

                    b.HasKey("Id");

                    b.ToTable("GoTrackRoutes", (string)null);
                });

            modelBuilder.Entity("GoTrack.SmsBundles.SmsBundle", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("char(36)");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("CreationTime");

                    b.Property<Guid?>("CreatorId")
                        .HasColumnType("char(36)")
                        .HasColumnName("CreatorId");

                    b.Property<Guid?>("DeleterId")
                        .HasColumnType("char(36)")
                        .HasColumnName("DeleterId");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("DeletionTime");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint(1)")
                        .HasDefaultValue(false)
                        .HasColumnName("IsDeleted");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("LastModificationTime");

                    b.Property<Guid?>("LastModifierId")
                        .HasColumnType("char(36)")
                        .HasColumnName("LastModifierId");

                    b.Property<int>("MessagesCount")
                        .HasColumnType("int");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<decimal>("Price")
                        .HasColumnType("decimal(65,30)");

                    b.HasKey("Id");

                    b.ToTable("GoTrackSmsBundles", (string)null);
                });

            modelBuilder.Entity("GoTrack.StopPoints.StopPoint", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("char(36)");

                    b.Property<int>("Color")
                        .HasColumnType("int");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .HasMaxLength(40)
                        .HasColumnType("varchar(40)")
                        .HasColumnName("ConcurrencyStamp");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("CreationTime");

                    b.Property<Guid?>("CreatorId")
                        .HasColumnType("char(36)")
                        .HasColumnName("CreatorId");

                    b.Property<Guid?>("DeleterId")
                        .HasColumnType("char(36)")
                        .HasColumnName("DeleterId");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("DeletionTime");

                    b.Property<string>("ExtraProperties")
                        .IsRequired()
                        .HasColumnType("longtext")
                        .HasColumnName("ExtraProperties");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint(1)")
                        .HasDefaultValue(false)
                        .HasColumnName("IsDeleted");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("LastModificationTime");

                    b.Property<Guid?>("LastModifierId")
                        .HasColumnType("char(36)")
                        .HasColumnName("LastModifierId");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<Point>("Point")
                        .IsRequired()
                        .HasColumnType("point");

                    b.Property<Guid>("TrackAccountId")
                        .HasColumnType("char(36)");

                    b.HasKey("Id");

                    b.ToTable("GoTrackStopPoints", (string)null);
                });

            modelBuilder.Entity("GoTrack.TrackAccounts.TrackAccount", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("char(36)");

                    b.Property<int>("AccountType")
                        .HasColumnType("int");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .HasMaxLength(40)
                        .HasColumnType("varchar(40)")
                        .HasColumnName("ConcurrencyStamp");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("CreationTime");

                    b.Property<Guid?>("CreatorId")
                        .HasColumnType("char(36)")
                        .HasColumnName("CreatorId");

                    b.Property<Guid?>("DeleterId")
                        .HasColumnType("char(36)")
                        .HasColumnName("DeleterId");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("DeletionTime");

                    b.Property<string>("ExtraProperties")
                        .IsRequired()
                        .HasColumnType("longtext")
                        .HasColumnName("ExtraProperties");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint(1)")
                        .HasDefaultValue(false)
                        .HasColumnName("IsDeleted");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("LastModificationTime");

                    b.Property<Guid?>("LastModifierId")
                        .HasColumnType("char(36)")
                        .HasColumnName("LastModifierId");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.HasKey("Id");

                    b.ToTable("GoTrackTrackAccounts", (string)null);

                    b.UseTptMappingStrategy();
                });

            modelBuilder.Entity("GoTrack.TrackAccounts.TrackAccountSubscriptions.TrackAccountSubscription", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("char(36)");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .HasMaxLength(40)
                        .HasColumnType("varchar(40)")
                        .HasColumnName("ConcurrencyStamp");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("CreationTime");

                    b.Property<Guid?>("CreatorId")
                        .HasColumnType("char(36)")
                        .HasColumnName("CreatorId");

                    b.Property<Guid?>("DeleterId")
                        .HasColumnType("char(36)")
                        .HasColumnName("DeleterId");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("DeletionTime");

                    b.Property<string>("ExtraProperties")
                        .IsRequired()
                        .HasColumnType("longtext")
                        .HasColumnName("ExtraProperties");

                    b.Property<DateTime>("From")
                        .HasColumnType("datetime(6)");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint(1)")
                        .HasDefaultValue(false)
                        .HasColumnName("IsDeleted");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("LastModificationTime");

                    b.Property<Guid?>("LastModifierId")
                        .HasColumnType("char(36)")
                        .HasColumnName("LastModifierId");

                    b.Property<DateTime?>("LastNotificationAt")
                        .HasColumnType("datetime(6)");

                    b.Property<int>("SmsBundleCount")
                        .HasColumnType("int");

                    b.Property<int>("State")
                        .HasColumnType("int");

                    b.Property<string>("SubscriptionPlanKey")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<DateTime>("To")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid>("TrackAccountId")
                        .HasColumnType("char(36)");

                    b.Property<int>("UserCount")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("TrackAccountId");

                    b.ToTable("GoTrackTrackAccountSubscriptions", (string)null);
                });

            modelBuilder.Entity("GoTrack.TrackableEntities.TrackableEntityAssociation", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("char(36)");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("CreationTime");

                    b.Property<Guid?>("CreatorId")
                        .HasColumnType("char(36)")
                        .HasColumnName("CreatorId");

                    b.Property<int>("EntityType")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(0);

                    b.HasKey("Id");

                    b.ToTable("GoTrackTrackableEntityAssociations", (string)null);

                    b.HasDiscriminator<int>("EntityType");

                    b.UseTphMappingStrategy();
                });

            modelBuilder.Entity("GoTrack.TripTemplates.TripTemplate", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("char(36)");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .HasMaxLength(40)
                        .HasColumnType("varchar(40)")
                        .HasColumnName("ConcurrencyStamp");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("CreationTime");

                    b.Property<Guid?>("CreatorId")
                        .HasColumnType("char(36)")
                        .HasColumnName("CreatorId");

                    b.Property<Guid?>("DeleterId")
                        .HasColumnType("char(36)")
                        .HasColumnName("DeleterId");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("DeletionTime");

                    b.Property<string>("ExtraProperties")
                        .IsRequired()
                        .HasColumnType("longtext")
                        .HasColumnName("ExtraProperties");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint(1)")
                        .HasDefaultValue(false)
                        .HasColumnName("IsDeleted");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("LastModificationTime");

                    b.Property<Guid?>("LastModifierId")
                        .HasColumnType("char(36)")
                        .HasColumnName("LastModifierId");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<Guid>("TrackAccountId")
                        .HasColumnType("char(36)");

                    b.HasKey("Id");

                    b.ToTable("GoTrackTripTemplates", (string)null);
                });

            modelBuilder.Entity("GoTrack.Trips.Trip", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("char(36)");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .HasMaxLength(40)
                        .HasColumnType("varchar(40)")
                        .HasColumnName("ConcurrencyStamp");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("CreationTime");

                    b.Property<Guid?>("CreatorId")
                        .HasColumnType("char(36)")
                        .HasColumnName("CreatorId");

                    b.Property<Guid?>("DeleterId")
                        .HasColumnType("char(36)")
                        .HasColumnName("DeleterId");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("DeletionTime");

                    b.Property<string>("ExtraProperties")
                        .IsRequired()
                        .HasColumnType("longtext")
                        .HasColumnName("ExtraProperties");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint(1)")
                        .HasDefaultValue(false)
                        .HasColumnName("IsDeleted");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("LastModificationTime");

                    b.Property<Guid?>("LastModifierId")
                        .HasColumnType("char(36)")
                        .HasColumnName("LastModifierId");

                    b.Property<Guid>("TripTemplateId")
                        .HasColumnType("char(36)");

                    b.Property<int>("TripType")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(0);

                    b.HasKey("Id");

                    b.ToTable("GoTrackTrips", (string)null);

                    b.HasDiscriminator<int>("TripType");

                    b.UseTphMappingStrategy();
                });

            modelBuilder.Entity("GoTrack.UserDeviceTokens.UserDeviceToken", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("char(36)");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .HasMaxLength(40)
                        .HasColumnType("varchar(40)")
                        .HasColumnName("ConcurrencyStamp");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("CreationTime");

                    b.Property<Guid?>("CreatorId")
                        .HasColumnType("char(36)")
                        .HasColumnName("CreatorId");

                    b.Property<Guid?>("DeleterId")
                        .HasColumnType("char(36)")
                        .HasColumnName("DeleterId");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("DeletionTime");

                    b.Property<string>("DeviceToken")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<string>("ExtraProperties")
                        .IsRequired()
                        .HasColumnType("longtext")
                        .HasColumnName("ExtraProperties");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint(1)")
                        .HasDefaultValue(false)
                        .HasColumnName("IsDeleted");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("LastModificationTime");

                    b.Property<Guid?>("LastModifierId")
                        .HasColumnType("char(36)")
                        .HasColumnName("LastModifierId");

                    b.Property<Guid>("UserId")
                        .HasColumnType("char(36)");

                    b.HasKey("Id");

                    b.ToTable("UserDeviceToken", (string)null);
                });

            modelBuilder.Entity("GoTrack.UserTrackAccountAssociations.UserTrackAccountAssociation", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("char(36)");

                    b.Property<int>("AssociationType")
                        .HasColumnType("int");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("CreationTime");

                    b.Property<Guid?>("CreatorId")
                        .HasColumnType("char(36)")
                        .HasColumnName("CreatorId");

                    b.Property<Guid?>("DeleterId")
                        .HasColumnType("char(36)")
                        .HasColumnName("DeleterId");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("DeletionTime");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint(1)")
                        .HasDefaultValue(false)
                        .HasColumnName("IsDeleted");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("LastModificationTime");

                    b.Property<Guid?>("LastModifierId")
                        .HasColumnType("char(36)")
                        .HasColumnName("LastModifierId");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<string>("PhoneNumber")
                        .HasColumnType("longtext");

                    b.Property<int>("Status")
                        .HasColumnType("int");

                    b.Property<Guid>("TrackAccountId")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("UserId")
                        .HasColumnType("char(36)");

                    b.HasKey("Id");

                    b.HasIndex("TrackAccountId");

                    b.ToTable("GoTrackUserTrackAccountAssociations", (string)null);
                });

            modelBuilder.Entity("GoTrack.VehicleDeviceEventLogs.VehicleDeviceEventLog", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("char(36)");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .HasMaxLength(40)
                        .HasColumnType("varchar(40)")
                        .HasColumnName("ConcurrencyStamp");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("CreationTime");

                    b.Property<Guid?>("CreatorId")
                        .HasColumnType("char(36)")
                        .HasColumnName("CreatorId");

                    b.Property<Guid?>("DeleterId")
                        .HasColumnType("char(36)")
                        .HasColumnName("DeleterId");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("DeletionTime");

                    b.Property<Guid>("DeviceId")
                        .HasColumnType("char(36)");

                    b.Property<int>("EventName")
                        .HasColumnType("int");

                    b.Property<string>("ExtraProperties")
                        .IsRequired()
                        .HasColumnType("longtext")
                        .HasColumnName("ExtraProperties");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint(1)")
                        .HasDefaultValue(false)
                        .HasColumnName("IsDeleted");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("LastModificationTime");

                    b.Property<Guid?>("LastModifierId")
                        .HasColumnType("char(36)")
                        .HasColumnName("LastModifierId");

                    b.Property<Guid>("VehicleId")
                        .HasColumnType("char(36)");

                    b.HasKey("Id");

                    b.HasIndex("DeviceId");

                    b.HasIndex("VehicleId");

                    b.ToTable("GoTrackVehicleDeviceEventLogs", (string)null);
                });

            modelBuilder.Entity("GoTrack.VehicleGroups.VehicleGroup", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("char(36)");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .HasMaxLength(40)
                        .HasColumnType("varchar(40)")
                        .HasColumnName("ConcurrencyStamp");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("CreationTime");

                    b.Property<Guid?>("CreatorId")
                        .HasColumnType("char(36)")
                        .HasColumnName("CreatorId");

                    b.Property<Guid?>("DeleterId")
                        .HasColumnType("char(36)")
                        .HasColumnName("DeleterId");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("DeletionTime");

                    b.Property<string>("ExtraProperties")
                        .IsRequired()
                        .HasColumnType("longtext")
                        .HasColumnName("ExtraProperties");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint(1)")
                        .HasDefaultValue(false)
                        .HasColumnName("IsDeleted");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("LastModificationTime");

                    b.Property<Guid?>("LastModifierId")
                        .HasColumnType("char(36)")
                        .HasColumnName("LastModifierId");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<Guid>("TrackAccountId")
                        .HasColumnType("char(36)");

                    b.HasKey("Id");

                    b.ToTable("GoTrackVehicleGroups", (string)null);
                });

            modelBuilder.Entity("GoTrack.Vehicles.Vehicle", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("char(36)");

                    b.Property<int>("Color")
                        .HasColumnType("int");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .HasMaxLength(40)
                        .HasColumnType("varchar(40)")
                        .HasColumnName("ConcurrencyStamp");

                    b.Property<double>("ConsumptionRate")
                        .HasColumnType("double");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("CreationTime");

                    b.Property<Guid?>("CreatorId")
                        .HasColumnType("char(36)")
                        .HasColumnName("CreatorId");

                    b.Property<Guid?>("DeleterId")
                        .HasColumnType("char(36)")
                        .HasColumnName("DeleterId");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("DeletionTime");

                    b.Property<string>("ExtraProperties")
                        .IsRequired()
                        .HasColumnType("longtext")
                        .HasColumnName("ExtraProperties");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint(1)")
                        .HasDefaultValue(false)
                        .HasColumnName("IsDeleted");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("LastModificationTime");

                    b.Property<Guid?>("LastModifierId")
                        .HasColumnType("char(36)")
                        .HasColumnName("LastModifierId");

                    b.Property<Guid>("TrackAccountId")
                        .HasColumnType("char(36)");

                    b.HasKey("Id");

                    b.HasIndex("TrackAccountId");

                    b.ToTable("GoTrackVehicles", (string)null);
                });

            modelBuilder.Entity("GoTrack.Vehicles.VehicleGroupVehicle", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("char(36)");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("CreationTime");

                    b.Property<Guid?>("CreatorId")
                        .HasColumnType("char(36)")
                        .HasColumnName("CreatorId");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("LastModificationTime");

                    b.Property<Guid?>("LastModifierId")
                        .HasColumnType("char(36)")
                        .HasColumnName("LastModifierId");

                    b.Property<Guid>("VehicleGroupId")
                        .HasColumnType("char(36)");

                    b.Property<Guid>("VehicleId")
                        .HasColumnType("char(36)");

                    b.HasKey("Id");

                    b.HasIndex("VehicleGroupId");

                    b.HasIndex("VehicleId");

                    b.ToTable("GoTrackVehicleGroupVehicles", (string)null);
                });

            modelBuilder.Entity("Microsoft.AspNetCore.DataProtection.EntityFrameworkCore.DataProtectionKey", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int");

                    MySqlPropertyBuilderExtensions.UseMySqlIdentityColumn(b.Property<int>("Id"));

                    b.Property<string>("FriendlyName")
                        .HasColumnType("longtext");

                    b.Property<string>("Xml")
                        .HasColumnType("longtext");

                    b.HasKey("Id");

                    b.ToTable("DataProtectionKeys");
                });

            modelBuilder.Entity("Notify.Notifications.Notification", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("char(36)");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .HasMaxLength(40)
                        .HasColumnType("varchar(40)")
                        .HasColumnName("ConcurrencyStamp");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("CreationTime");

                    b.Property<Guid?>("CreatorId")
                        .HasColumnType("char(36)")
                        .HasColumnName("CreatorId");

                    b.Property<Guid?>("DeleterId")
                        .HasColumnType("char(36)")
                        .HasColumnName("DeleterId");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("DeletionTime");

                    b.Property<string>("ExtraProperties")
                        .IsRequired()
                        .HasColumnType("longtext")
                        .HasColumnName("ExtraProperties");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint(1)")
                        .HasDefaultValue(false)
                        .HasColumnName("IsDeleted");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("LastModificationTime");

                    b.Property<Guid?>("LastModifierId")
                        .HasColumnType("char(36)")
                        .HasColumnName("LastModifierId");

                    b.HasKey("Id");

                    b.ToTable("NotifyNotifications", (string)null);
                });

            modelBuilder.Entity("Notify.Notifications.UserNotifications.UserNotification", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("char(36)");

                    b.Property<DateTime?>("CompletionDateTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .HasMaxLength(40)
                        .HasColumnType("varchar(40)")
                        .HasColumnName("ConcurrencyStamp");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("CreationTime");

                    b.Property<Guid?>("CreatorId")
                        .HasColumnType("char(36)")
                        .HasColumnName("CreatorId");

                    b.Property<string>("ExtraProperties")
                        .IsRequired()
                        .HasColumnType("longtext")
                        .HasColumnName("ExtraProperties");

                    b.Property<string>("FailureReason")
                        .HasColumnType("longtext");

                    b.Property<Guid>("NotificationId")
                        .HasColumnType("char(36)");

                    b.Property<string>("NotificationMethod")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<bool?>("Success")
                        .HasColumnType("tinyint(1)");

                    b.Property<Guid?>("TrackAccountId")
                        .HasColumnType("char(36)");

                    b.Property<Guid>("UserId")
                        .HasColumnType("char(36)");

                    b.HasKey("Id");

                    b.ToTable("NotifyUserNotifications", (string)null);
                });

            modelBuilder.Entity("Volo.Abp.AuditLogging.AuditLog", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("char(36)");

                    b.Property<string>("ApplicationName")
                        .HasMaxLength(96)
                        .HasColumnType("varchar(96)")
                        .HasColumnName("ApplicationName");

                    b.Property<string>("BrowserInfo")
                        .HasMaxLength(512)
                        .HasColumnType("varchar(512)")
                        .HasColumnName("BrowserInfo");

                    b.Property<string>("ClientId")
                        .HasMaxLength(64)
                        .HasColumnType("varchar(64)")
                        .HasColumnName("ClientId");

                    b.Property<string>("ClientIpAddress")
                        .HasMaxLength(64)
                        .HasColumnType("varchar(64)")
                        .HasColumnName("ClientIpAddress");

                    b.Property<string>("ClientName")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)")
                        .HasColumnName("ClientName");

                    b.Property<string>("Comments")
                        .HasMaxLength(256)
                        .HasColumnType("varchar(256)")
                        .HasColumnName("Comments");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .HasMaxLength(40)
                        .HasColumnType("varchar(40)")
                        .HasColumnName("ConcurrencyStamp");

                    b.Property<string>("CorrelationId")
                        .HasMaxLength(64)
                        .HasColumnType("varchar(64)")
                        .HasColumnName("CorrelationId");

                    b.Property<string>("Exceptions")
                        .HasColumnType("longtext");

                    b.Property<int>("ExecutionDuration")
                        .HasColumnType("int")
                        .HasColumnName("ExecutionDuration");

                    b.Property<DateTime>("ExecutionTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("ExtraProperties")
                        .IsRequired()
                        .HasColumnType("longtext")
                        .HasColumnName("ExtraProperties");

                    b.Property<string>("HttpMethod")
                        .HasMaxLength(16)
                        .HasColumnType("varchar(16)")
                        .HasColumnName("HttpMethod");

                    b.Property<int?>("HttpStatusCode")
                        .HasColumnType("int")
                        .HasColumnName("HttpStatusCode");

                    b.Property<Guid?>("ImpersonatorTenantId")
                        .HasColumnType("char(36)")
                        .HasColumnName("ImpersonatorTenantId");

                    b.Property<string>("ImpersonatorTenantName")
                        .HasMaxLength(64)
                        .HasColumnType("varchar(64)")
                        .HasColumnName("ImpersonatorTenantName");

                    b.Property<Guid?>("ImpersonatorUserId")
                        .HasColumnType("char(36)")
                        .HasColumnName("ImpersonatorUserId");

                    b.Property<string>("ImpersonatorUserName")
                        .HasMaxLength(256)
                        .HasColumnType("varchar(256)")
                        .HasColumnName("ImpersonatorUserName");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("char(36)")
                        .HasColumnName("TenantId");

                    b.Property<string>("TenantName")
                        .HasMaxLength(64)
                        .HasColumnType("varchar(64)")
                        .HasColumnName("TenantName");

                    b.Property<string>("Url")
                        .HasMaxLength(256)
                        .HasColumnType("varchar(256)")
                        .HasColumnName("Url");

                    b.Property<Guid?>("UserId")
                        .HasColumnType("char(36)")
                        .HasColumnName("UserId");

                    b.Property<string>("UserName")
                        .HasMaxLength(256)
                        .HasColumnType("varchar(256)")
                        .HasColumnName("UserName");

                    b.HasKey("Id");

                    b.HasIndex("TenantId", "ExecutionTime");

                    b.HasIndex("TenantId", "UserId", "ExecutionTime");

                    b.ToTable("AbpAuditLogs", (string)null);
                });

            modelBuilder.Entity("Volo.Abp.AuditLogging.AuditLogAction", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("char(36)");

                    b.Property<Guid>("AuditLogId")
                        .HasColumnType("char(36)")
                        .HasColumnName("AuditLogId");

                    b.Property<int>("ExecutionDuration")
                        .HasColumnType("int")
                        .HasColumnName("ExecutionDuration");

                    b.Property<DateTime>("ExecutionTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("ExecutionTime");

                    b.Property<string>("ExtraProperties")
                        .HasColumnType("longtext")
                        .HasColumnName("ExtraProperties");

                    b.Property<string>("MethodName")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)")
                        .HasColumnName("MethodName");

                    b.Property<string>("Parameters")
                        .HasMaxLength(2000)
                        .HasColumnType("varchar(2000)")
                        .HasColumnName("Parameters");

                    b.Property<string>("ServiceName")
                        .HasMaxLength(256)
                        .HasColumnType("varchar(256)")
                        .HasColumnName("ServiceName");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("char(36)")
                        .HasColumnName("TenantId");

                    b.HasKey("Id");

                    b.HasIndex("AuditLogId");

                    b.HasIndex("TenantId", "ServiceName", "MethodName", "ExecutionTime");

                    b.ToTable("AbpAuditLogActions", (string)null);
                });

            modelBuilder.Entity("Volo.Abp.AuditLogging.EntityChange", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("char(36)");

                    b.Property<Guid>("AuditLogId")
                        .HasColumnType("char(36)")
                        .HasColumnName("AuditLogId");

                    b.Property<DateTime>("ChangeTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("ChangeTime");

                    b.Property<byte>("ChangeType")
                        .HasColumnType("tinyint unsigned")
                        .HasColumnName("ChangeType");

                    b.Property<string>("EntityId")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)")
                        .HasColumnName("EntityId");

                    b.Property<Guid?>("EntityTenantId")
                        .HasColumnType("char(36)");

                    b.Property<string>("EntityTypeFullName")
                        .IsRequired()
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)")
                        .HasColumnName("EntityTypeFullName");

                    b.Property<string>("ExtraProperties")
                        .HasColumnType("longtext")
                        .HasColumnName("ExtraProperties");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("char(36)")
                        .HasColumnName("TenantId");

                    b.HasKey("Id");

                    b.HasIndex("AuditLogId");

                    b.HasIndex("TenantId", "EntityTypeFullName", "EntityId");

                    b.ToTable("AbpEntityChanges", (string)null);
                });

            modelBuilder.Entity("Volo.Abp.AuditLogging.EntityPropertyChange", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("char(36)");

                    b.Property<Guid>("EntityChangeId")
                        .HasColumnType("char(36)");

                    b.Property<string>("NewValue")
                        .HasMaxLength(512)
                        .HasColumnType("varchar(512)")
                        .HasColumnName("NewValue");

                    b.Property<string>("OriginalValue")
                        .HasMaxLength(512)
                        .HasColumnType("varchar(512)")
                        .HasColumnName("OriginalValue");

                    b.Property<string>("PropertyName")
                        .IsRequired()
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)")
                        .HasColumnName("PropertyName");

                    b.Property<string>("PropertyTypeFullName")
                        .IsRequired()
                        .HasMaxLength(64)
                        .HasColumnType("varchar(64)")
                        .HasColumnName("PropertyTypeFullName");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("char(36)")
                        .HasColumnName("TenantId");

                    b.HasKey("Id");

                    b.HasIndex("EntityChangeId");

                    b.ToTable("AbpEntityPropertyChanges", (string)null);
                });

            modelBuilder.Entity("Volo.Abp.BackgroundJobs.BackgroundJobRecord", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("char(36)");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .HasMaxLength(40)
                        .HasColumnType("varchar(40)")
                        .HasColumnName("ConcurrencyStamp");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("CreationTime");

                    b.Property<string>("ExtraProperties")
                        .IsRequired()
                        .HasColumnType("longtext")
                        .HasColumnName("ExtraProperties");

                    b.Property<bool>("IsAbandoned")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint(1)")
                        .HasDefaultValue(false);

                    b.Property<string>("JobArgs")
                        .IsRequired()
                        .HasMaxLength(1048576)
                        .HasColumnType("longtext");

                    b.Property<string>("JobName")
                        .IsRequired()
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<DateTime?>("LastTryTime")
                        .HasColumnType("datetime(6)");

                    b.Property<DateTime>("NextTryTime")
                        .HasColumnType("datetime(6)");

                    b.Property<byte>("Priority")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint unsigned")
                        .HasDefaultValue((byte)15);

                    b.Property<short>("TryCount")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("smallint")
                        .HasDefaultValue((short)0);

                    b.HasKey("Id");

                    b.HasIndex("IsAbandoned", "NextTryTime");

                    b.ToTable("AbpBackgroundJobs", (string)null);
                });

            modelBuilder.Entity("Volo.Abp.BlobStoring.Database.DatabaseBlob", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("char(36)");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .HasMaxLength(40)
                        .HasColumnType("varchar(40)")
                        .HasColumnName("ConcurrencyStamp");

                    b.Property<Guid>("ContainerId")
                        .HasColumnType("char(36)");

                    b.Property<byte[]>("Content")
                        .HasMaxLength(2147483647)
                        .HasColumnType("longblob");

                    b.Property<string>("ExtraProperties")
                        .IsRequired()
                        .HasColumnType("longtext")
                        .HasColumnName("ExtraProperties");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("varchar(256)");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("char(36)")
                        .HasColumnName("TenantId");

                    b.HasKey("Id");

                    b.HasIndex("ContainerId");

                    b.HasIndex("TenantId", "ContainerId", "Name");

                    b.ToTable("AbpBlobs", (string)null);
                });

            modelBuilder.Entity("Volo.Abp.BlobStoring.Database.DatabaseBlobContainer", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("char(36)");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .HasMaxLength(40)
                        .HasColumnType("varchar(40)")
                        .HasColumnName("ConcurrencyStamp");

                    b.Property<string>("ExtraProperties")
                        .IsRequired()
                        .HasColumnType("longtext")
                        .HasColumnName("ExtraProperties");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("char(36)")
                        .HasColumnName("TenantId");

                    b.HasKey("Id");

                    b.HasIndex("TenantId", "Name");

                    b.ToTable("AbpBlobContainers", (string)null);
                });

            modelBuilder.Entity("Volo.Abp.FeatureManagement.FeatureDefinitionRecord", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("char(36)");

                    b.Property<string>("AllowedProviders")
                        .HasMaxLength(256)
                        .HasColumnType("varchar(256)");

                    b.Property<string>("DefaultValue")
                        .HasMaxLength(256)
                        .HasColumnType("varchar(256)");

                    b.Property<string>("Description")
                        .HasMaxLength(256)
                        .HasColumnType("varchar(256)");

                    b.Property<string>("DisplayName")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("varchar(256)");

                    b.Property<string>("ExtraProperties")
                        .HasColumnType("longtext")
                        .HasColumnName("ExtraProperties");

                    b.Property<string>("GroupName")
                        .IsRequired()
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<bool>("IsAvailableToHost")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsVisibleToClients")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("ParentName")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("ValueType")
                        .HasMaxLength(2048)
                        .HasColumnType("varchar(2048)");

                    b.HasKey("Id");

                    b.HasIndex("GroupName");

                    b.HasIndex("Name")
                        .IsUnique();

                    b.ToTable("AbpFeatures", (string)null);
                });

            modelBuilder.Entity("Volo.Abp.FeatureManagement.FeatureGroupDefinitionRecord", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("char(36)");

                    b.Property<string>("DisplayName")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("varchar(256)");

                    b.Property<string>("ExtraProperties")
                        .HasColumnType("longtext")
                        .HasColumnName("ExtraProperties");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.HasKey("Id");

                    b.HasIndex("Name")
                        .IsUnique();

                    b.ToTable("AbpFeatureGroups", (string)null);
                });

            modelBuilder.Entity("Volo.Abp.FeatureManagement.FeatureValue", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("char(36)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("ProviderKey")
                        .HasMaxLength(64)
                        .HasColumnType("varchar(64)");

                    b.Property<string>("ProviderName")
                        .HasMaxLength(64)
                        .HasColumnType("varchar(64)");

                    b.Property<string>("Value")
                        .IsRequired()
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.HasKey("Id");

                    b.HasIndex("Name", "ProviderName", "ProviderKey")
                        .IsUnique();

                    b.ToTable("AbpFeatureValues", (string)null);
                });

            modelBuilder.Entity("Volo.Abp.Identity.IdentityClaimType", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("char(36)");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .HasMaxLength(40)
                        .HasColumnType("varchar(40)")
                        .HasColumnName("ConcurrencyStamp");

                    b.Property<string>("Description")
                        .HasMaxLength(256)
                        .HasColumnType("varchar(256)");

                    b.Property<string>("ExtraProperties")
                        .IsRequired()
                        .HasColumnType("longtext")
                        .HasColumnName("ExtraProperties");

                    b.Property<bool>("IsStatic")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("varchar(256)");

                    b.Property<string>("Regex")
                        .HasMaxLength(512)
                        .HasColumnType("varchar(512)");

                    b.Property<string>("RegexDescription")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<bool>("Required")
                        .HasColumnType("tinyint(1)");

                    b.Property<int>("ValueType")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.ToTable("AbpClaimTypes", (string)null);
                });

            modelBuilder.Entity("Volo.Abp.Identity.IdentityLinkUser", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("SourceTenantId")
                        .HasColumnType("char(36)");

                    b.Property<Guid>("SourceUserId")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("TargetTenantId")
                        .HasColumnType("char(36)");

                    b.Property<Guid>("TargetUserId")
                        .HasColumnType("char(36)");

                    b.HasKey("Id");

                    b.HasIndex("SourceUserId", "SourceTenantId", "TargetUserId", "TargetTenantId")
                        .IsUnique();

                    b.ToTable("AbpLinkUsers", (string)null);
                });

            modelBuilder.Entity("Volo.Abp.Identity.IdentityRole", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("char(36)");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .HasMaxLength(40)
                        .HasColumnType("varchar(40)")
                        .HasColumnName("ConcurrencyStamp");

                    b.Property<int>("EntityVersion")
                        .HasColumnType("int");

                    b.Property<string>("ExtraProperties")
                        .IsRequired()
                        .HasColumnType("longtext")
                        .HasColumnName("ExtraProperties");

                    b.Property<bool>("IsDefault")
                        .HasColumnType("tinyint(1)")
                        .HasColumnName("IsDefault");

                    b.Property<bool>("IsPublic")
                        .HasColumnType("tinyint(1)")
                        .HasColumnName("IsPublic");

                    b.Property<bool>("IsStatic")
                        .HasColumnType("tinyint(1)")
                        .HasColumnName("IsStatic");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("varchar(256)");

                    b.Property<string>("NormalizedName")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("varchar(256)");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("char(36)")
                        .HasColumnName("TenantId");

                    b.HasKey("Id");

                    b.HasIndex("NormalizedName");

                    b.ToTable("AbpRoles", (string)null);
                });

            modelBuilder.Entity("Volo.Abp.Identity.IdentityRoleClaim", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("char(36)");

                    b.Property<string>("ClaimType")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("varchar(256)");

                    b.Property<string>("ClaimValue")
                        .HasMaxLength(1024)
                        .HasColumnType("varchar(1024)");

                    b.Property<Guid>("RoleId")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("char(36)")
                        .HasColumnName("TenantId");

                    b.HasKey("Id");

                    b.HasIndex("RoleId");

                    b.ToTable("AbpRoleClaims", (string)null);
                });

            modelBuilder.Entity("Volo.Abp.Identity.IdentitySecurityLog", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("char(36)");

                    b.Property<string>("Action")
                        .HasMaxLength(96)
                        .HasColumnType("varchar(96)");

                    b.Property<string>("ApplicationName")
                        .HasMaxLength(96)
                        .HasColumnType("varchar(96)");

                    b.Property<string>("BrowserInfo")
                        .HasMaxLength(512)
                        .HasColumnType("varchar(512)");

                    b.Property<string>("ClientId")
                        .HasMaxLength(64)
                        .HasColumnType("varchar(64)");

                    b.Property<string>("ClientIpAddress")
                        .HasMaxLength(64)
                        .HasColumnType("varchar(64)");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .HasMaxLength(40)
                        .HasColumnType("varchar(40)")
                        .HasColumnName("ConcurrencyStamp");

                    b.Property<string>("CorrelationId")
                        .HasMaxLength(64)
                        .HasColumnType("varchar(64)");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("ExtraProperties")
                        .IsRequired()
                        .HasColumnType("longtext")
                        .HasColumnName("ExtraProperties");

                    b.Property<string>("Identity")
                        .HasMaxLength(96)
                        .HasColumnType("varchar(96)");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("char(36)")
                        .HasColumnName("TenantId");

                    b.Property<string>("TenantName")
                        .HasMaxLength(64)
                        .HasColumnType("varchar(64)");

                    b.Property<Guid?>("UserId")
                        .HasColumnType("char(36)");

                    b.Property<string>("UserName")
                        .HasMaxLength(256)
                        .HasColumnType("varchar(256)");

                    b.HasKey("Id");

                    b.HasIndex("TenantId", "Action");

                    b.HasIndex("TenantId", "ApplicationName");

                    b.HasIndex("TenantId", "Identity");

                    b.HasIndex("TenantId", "UserId");

                    b.ToTable("AbpSecurityLogs", (string)null);
                });

            modelBuilder.Entity("Volo.Abp.Identity.IdentitySession", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("char(36)");

                    b.Property<string>("ClientId")
                        .HasMaxLength(64)
                        .HasColumnType("varchar(64)");

                    b.Property<string>("Device")
                        .IsRequired()
                        .HasMaxLength(64)
                        .HasColumnType("varchar(64)");

                    b.Property<string>("DeviceInfo")
                        .HasMaxLength(64)
                        .HasColumnType("varchar(64)");

                    b.Property<string>("IpAddresses")
                        .HasMaxLength(256)
                        .HasColumnType("varchar(256)");

                    b.Property<DateTime?>("LastAccessed")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("SessionId")
                        .IsRequired()
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<DateTime>("SignedIn")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("char(36)")
                        .HasColumnName("TenantId");

                    b.Property<Guid>("UserId")
                        .HasColumnType("char(36)");

                    b.HasKey("Id");

                    b.HasIndex("Device");

                    b.HasIndex("SessionId");

                    b.HasIndex("TenantId", "UserId");

                    b.ToTable("AbpSessions", (string)null);
                });

            modelBuilder.Entity("Volo.Abp.Identity.IdentityUser", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("char(36)");

                    b.Property<int>("AccessFailedCount")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(0)
                        .HasColumnName("AccessFailedCount");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .HasMaxLength(40)
                        .HasColumnType("varchar(40)")
                        .HasColumnName("ConcurrencyStamp");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("CreationTime");

                    b.Property<Guid?>("CreatorId")
                        .HasColumnType("char(36)")
                        .HasColumnName("CreatorId");

                    b.Property<Guid?>("DeleterId")
                        .HasColumnType("char(36)")
                        .HasColumnName("DeleterId");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("DeletionTime");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("varchar(256)")
                        .HasColumnName("Email");

                    b.Property<bool>("EmailConfirmed")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint(1)")
                        .HasDefaultValue(false)
                        .HasColumnName("EmailConfirmed");

                    b.Property<int>("EntityVersion")
                        .HasColumnType("int");

                    b.Property<string>("ExtraProperties")
                        .IsRequired()
                        .HasColumnType("longtext")
                        .HasColumnName("ExtraProperties");

                    b.Property<bool>("IsActive")
                        .HasColumnType("tinyint(1)")
                        .HasColumnName("IsActive");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint(1)")
                        .HasDefaultValue(false)
                        .HasColumnName("IsDeleted");

                    b.Property<bool>("IsExternal")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint(1)")
                        .HasDefaultValue(false)
                        .HasColumnName("IsExternal");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("LastModificationTime");

                    b.Property<Guid?>("LastModifierId")
                        .HasColumnType("char(36)")
                        .HasColumnName("LastModifierId");

                    b.Property<DateTimeOffset?>("LastPasswordChangeTime")
                        .HasColumnType("datetime(6)");

                    b.Property<bool>("LockoutEnabled")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint(1)")
                        .HasDefaultValue(false)
                        .HasColumnName("LockoutEnabled");

                    b.Property<DateTimeOffset?>("LockoutEnd")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("Name")
                        .HasMaxLength(64)
                        .HasColumnType("varchar(64)")
                        .HasColumnName("Name");

                    b.Property<string>("NormalizedEmail")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("varchar(256)")
                        .HasColumnName("NormalizedEmail");

                    b.Property<string>("NormalizedUserName")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("varchar(256)")
                        .HasColumnName("NormalizedUserName");

                    b.Property<string>("PasswordHash")
                        .HasMaxLength(256)
                        .HasColumnType("varchar(256)")
                        .HasColumnName("PasswordHash");

                    b.Property<string>("PhoneNumber")
                        .HasMaxLength(16)
                        .HasColumnType("varchar(16)")
                        .HasColumnName("PhoneNumber");

                    b.Property<bool>("PhoneNumberConfirmed")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint(1)")
                        .HasDefaultValue(false)
                        .HasColumnName("PhoneNumberConfirmed");

                    b.Property<string>("SecurityStamp")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("varchar(256)")
                        .HasColumnName("SecurityStamp");

                    b.Property<bool>("ShouldChangePasswordOnNextLogin")
                        .HasColumnType("tinyint(1)");

                    b.Property<int>("SubClass")
                        .HasColumnType("int");

                    b.Property<string>("Surname")
                        .HasMaxLength(64)
                        .HasColumnType("varchar(64)")
                        .HasColumnName("Surname");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("char(36)")
                        .HasColumnName("TenantId");

                    b.Property<bool>("TwoFactorEnabled")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint(1)")
                        .HasDefaultValue(false)
                        .HasColumnName("TwoFactorEnabled");

                    b.Property<string>("UserName")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("varchar(256)")
                        .HasColumnName("UserName");

                    b.HasKey("Id");

                    b.HasIndex("Email");

                    b.HasIndex("NormalizedEmail");

                    b.HasIndex("NormalizedUserName");

                    b.HasIndex("UserName");

                    b.ToTable("AbpUsers", (string)null);
                });

            modelBuilder.Entity("Volo.Abp.Identity.IdentityUserClaim", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("char(36)");

                    b.Property<string>("ClaimType")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("varchar(256)");

                    b.Property<string>("ClaimValue")
                        .HasMaxLength(1024)
                        .HasColumnType("varchar(1024)");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("char(36)")
                        .HasColumnName("TenantId");

                    b.Property<Guid>("UserId")
                        .HasColumnType("char(36)");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("AbpUserClaims", (string)null);
                });

            modelBuilder.Entity("Volo.Abp.Identity.IdentityUserDelegation", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("char(36)");

                    b.Property<DateTime>("EndTime")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid>("SourceUserId")
                        .HasColumnType("char(36)");

                    b.Property<DateTime>("StartTime")
                        .HasColumnType("datetime(6)");

                    b.Property<Guid>("TargetUserId")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("char(36)")
                        .HasColumnName("TenantId");

                    b.HasKey("Id");

                    b.ToTable("AbpUserDelegations", (string)null);
                });

            modelBuilder.Entity("Volo.Abp.Identity.IdentityUserLogin", b =>
                {
                    b.Property<Guid>("UserId")
                        .HasColumnType("char(36)");

                    b.Property<string>("LoginProvider")
                        .HasMaxLength(64)
                        .HasColumnType("varchar(64)");

                    b.Property<string>("ProviderDisplayName")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("ProviderKey")
                        .IsRequired()
                        .HasMaxLength(196)
                        .HasColumnType("varchar(196)");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("char(36)")
                        .HasColumnName("TenantId");

                    b.HasKey("UserId", "LoginProvider");

                    b.HasIndex("LoginProvider", "ProviderKey");

                    b.ToTable("AbpUserLogins", (string)null);
                });

            modelBuilder.Entity("Volo.Abp.Identity.IdentityUserOrganizationUnit", b =>
                {
                    b.Property<Guid>("OrganizationUnitId")
                        .HasColumnType("char(36)");

                    b.Property<Guid>("UserId")
                        .HasColumnType("char(36)");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("CreationTime");

                    b.Property<Guid?>("CreatorId")
                        .HasColumnType("char(36)")
                        .HasColumnName("CreatorId");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("char(36)")
                        .HasColumnName("TenantId");

                    b.HasKey("OrganizationUnitId", "UserId");

                    b.HasIndex("UserId", "OrganizationUnitId");

                    b.ToTable("AbpUserOrganizationUnits", (string)null);
                });

            modelBuilder.Entity("Volo.Abp.Identity.IdentityUserRole", b =>
                {
                    b.Property<Guid>("UserId")
                        .HasColumnType("char(36)");

                    b.Property<Guid>("RoleId")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("char(36)")
                        .HasColumnName("TenantId");

                    b.HasKey("UserId", "RoleId");

                    b.HasIndex("RoleId", "UserId");

                    b.ToTable("AbpUserRoles", (string)null);
                });

            modelBuilder.Entity("Volo.Abp.Identity.IdentityUserToken", b =>
                {
                    b.Property<Guid>("UserId")
                        .HasColumnType("char(36)");

                    b.Property<string>("LoginProvider")
                        .HasMaxLength(64)
                        .HasColumnType("varchar(64)");

                    b.Property<string>("Name")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("char(36)")
                        .HasColumnName("TenantId");

                    b.Property<string>("Value")
                        .HasColumnType("longtext");

                    b.HasKey("UserId", "LoginProvider", "Name");

                    b.ToTable("AbpUserTokens", (string)null);
                });

            modelBuilder.Entity("Volo.Abp.Identity.OrganizationUnit", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("char(36)");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(95)
                        .HasColumnType("varchar(95)")
                        .HasColumnName("Code");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .HasMaxLength(40)
                        .HasColumnType("varchar(40)")
                        .HasColumnName("ConcurrencyStamp");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("CreationTime");

                    b.Property<Guid?>("CreatorId")
                        .HasColumnType("char(36)")
                        .HasColumnName("CreatorId");

                    b.Property<Guid?>("DeleterId")
                        .HasColumnType("char(36)")
                        .HasColumnName("DeleterId");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("DeletionTime");

                    b.Property<string>("DisplayName")
                        .IsRequired()
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)")
                        .HasColumnName("DisplayName");

                    b.Property<int>("EntityVersion")
                        .HasColumnType("int");

                    b.Property<string>("ExtraProperties")
                        .IsRequired()
                        .HasColumnType("longtext")
                        .HasColumnName("ExtraProperties");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint(1)")
                        .HasDefaultValue(false)
                        .HasColumnName("IsDeleted");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("LastModificationTime");

                    b.Property<Guid?>("LastModifierId")
                        .HasColumnType("char(36)")
                        .HasColumnName("LastModifierId");

                    b.Property<Guid?>("ParentId")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("char(36)")
                        .HasColumnName("TenantId");

                    b.HasKey("Id");

                    b.HasIndex("Code");

                    b.HasIndex("ParentId");

                    b.ToTable("AbpOrganizationUnits", (string)null);
                });

            modelBuilder.Entity("Volo.Abp.Identity.OrganizationUnitRole", b =>
                {
                    b.Property<Guid>("OrganizationUnitId")
                        .HasColumnType("char(36)");

                    b.Property<Guid>("RoleId")
                        .HasColumnType("char(36)");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("CreationTime");

                    b.Property<Guid?>("CreatorId")
                        .HasColumnType("char(36)")
                        .HasColumnName("CreatorId");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("char(36)")
                        .HasColumnName("TenantId");

                    b.HasKey("OrganizationUnitId", "RoleId");

                    b.HasIndex("RoleId", "OrganizationUnitId");

                    b.ToTable("AbpOrganizationUnitRoles", (string)null);
                });

            modelBuilder.Entity("Volo.Abp.OpenIddict.Applications.OpenIddictApplication", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("char(36)");

                    b.Property<string>("ApplicationType")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("ClientId")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<string>("ClientSecret")
                        .HasColumnType("longtext");

                    b.Property<string>("ClientType")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("ClientUri")
                        .HasColumnType("longtext");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .HasMaxLength(40)
                        .HasColumnType("varchar(40)")
                        .HasColumnName("ConcurrencyStamp");

                    b.Property<string>("ConsentType")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("CreationTime");

                    b.Property<Guid?>("CreatorId")
                        .HasColumnType("char(36)")
                        .HasColumnName("CreatorId");

                    b.Property<Guid?>("DeleterId")
                        .HasColumnType("char(36)")
                        .HasColumnName("DeleterId");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("DeletionTime");

                    b.Property<string>("DisplayName")
                        .HasColumnType("longtext");

                    b.Property<string>("DisplayNames")
                        .HasColumnType("longtext");

                    b.Property<string>("ExtraProperties")
                        .IsRequired()
                        .HasColumnType("longtext")
                        .HasColumnName("ExtraProperties");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint(1)")
                        .HasDefaultValue(false)
                        .HasColumnName("IsDeleted");

                    b.Property<string>("JsonWebKeySet")
                        .HasColumnType("longtext");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("LastModificationTime");

                    b.Property<Guid?>("LastModifierId")
                        .HasColumnType("char(36)")
                        .HasColumnName("LastModifierId");

                    b.Property<string>("LogoUri")
                        .HasColumnType("longtext");

                    b.Property<string>("Permissions")
                        .HasColumnType("longtext");

                    b.Property<string>("PostLogoutRedirectUris")
                        .HasColumnType("longtext");

                    b.Property<string>("Properties")
                        .HasColumnType("longtext");

                    b.Property<string>("RedirectUris")
                        .HasColumnType("longtext");

                    b.Property<string>("Requirements")
                        .HasColumnType("longtext");

                    b.Property<string>("Settings")
                        .HasColumnType("longtext");

                    b.HasKey("Id");

                    b.HasIndex("ClientId");

                    b.ToTable("OpenIddictApplications", (string)null);
                });

            modelBuilder.Entity("Volo.Abp.OpenIddict.Authorizations.OpenIddictAuthorization", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("ApplicationId")
                        .HasColumnType("char(36)");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .HasMaxLength(40)
                        .HasColumnType("varchar(40)")
                        .HasColumnName("ConcurrencyStamp");

                    b.Property<DateTime?>("CreationDate")
                        .HasColumnType("datetime(6)");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("CreationTime");

                    b.Property<Guid?>("CreatorId")
                        .HasColumnType("char(36)")
                        .HasColumnName("CreatorId");

                    b.Property<Guid?>("DeleterId")
                        .HasColumnType("char(36)")
                        .HasColumnName("DeleterId");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("DeletionTime");

                    b.Property<string>("ExtraProperties")
                        .IsRequired()
                        .HasColumnType("longtext")
                        .HasColumnName("ExtraProperties");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint(1)")
                        .HasDefaultValue(false)
                        .HasColumnName("IsDeleted");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("LastModificationTime");

                    b.Property<Guid?>("LastModifierId")
                        .HasColumnType("char(36)")
                        .HasColumnName("LastModifierId");

                    b.Property<string>("Properties")
                        .HasColumnType("longtext");

                    b.Property<string>("Scopes")
                        .HasColumnType("longtext");

                    b.Property<string>("Status")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("Subject")
                        .HasMaxLength(400)
                        .HasColumnType("varchar(400)");

                    b.Property<string>("Type")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.HasKey("Id");

                    b.HasIndex("ApplicationId", "Status", "Subject", "Type");

                    b.ToTable("OpenIddictAuthorizations", (string)null);
                });

            modelBuilder.Entity("Volo.Abp.OpenIddict.Scopes.OpenIddictScope", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("char(36)");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .HasMaxLength(40)
                        .HasColumnType("varchar(40)")
                        .HasColumnName("ConcurrencyStamp");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("CreationTime");

                    b.Property<Guid?>("CreatorId")
                        .HasColumnType("char(36)")
                        .HasColumnName("CreatorId");

                    b.Property<Guid?>("DeleterId")
                        .HasColumnType("char(36)")
                        .HasColumnName("DeleterId");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("DeletionTime");

                    b.Property<string>("Description")
                        .HasColumnType("longtext");

                    b.Property<string>("Descriptions")
                        .HasColumnType("longtext");

                    b.Property<string>("DisplayName")
                        .HasColumnType("longtext");

                    b.Property<string>("DisplayNames")
                        .HasColumnType("longtext");

                    b.Property<string>("ExtraProperties")
                        .IsRequired()
                        .HasColumnType("longtext")
                        .HasColumnName("ExtraProperties");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint(1)")
                        .HasDefaultValue(false)
                        .HasColumnName("IsDeleted");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("LastModificationTime");

                    b.Property<Guid?>("LastModifierId")
                        .HasColumnType("char(36)")
                        .HasColumnName("LastModifierId");

                    b.Property<string>("Name")
                        .HasMaxLength(200)
                        .HasColumnType("varchar(200)");

                    b.Property<string>("Properties")
                        .HasColumnType("longtext");

                    b.Property<string>("Resources")
                        .HasColumnType("longtext");

                    b.HasKey("Id");

                    b.HasIndex("Name");

                    b.ToTable("OpenIddictScopes", (string)null);
                });

            modelBuilder.Entity("Volo.Abp.OpenIddict.Tokens.OpenIddictToken", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("ApplicationId")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("AuthorizationId")
                        .HasColumnType("char(36)");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .HasMaxLength(40)
                        .HasColumnType("varchar(40)")
                        .HasColumnName("ConcurrencyStamp");

                    b.Property<DateTime?>("CreationDate")
                        .HasColumnType("datetime(6)");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("CreationTime");

                    b.Property<Guid?>("CreatorId")
                        .HasColumnType("char(36)")
                        .HasColumnName("CreatorId");

                    b.Property<Guid?>("DeleterId")
                        .HasColumnType("char(36)")
                        .HasColumnName("DeleterId");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("DeletionTime");

                    b.Property<DateTime?>("ExpirationDate")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("ExtraProperties")
                        .IsRequired()
                        .HasColumnType("longtext")
                        .HasColumnName("ExtraProperties");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint(1)")
                        .HasDefaultValue(false)
                        .HasColumnName("IsDeleted");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("LastModificationTime");

                    b.Property<Guid?>("LastModifierId")
                        .HasColumnType("char(36)")
                        .HasColumnName("LastModifierId");

                    b.Property<string>("Payload")
                        .HasColumnType("longtext");

                    b.Property<string>("Properties")
                        .HasColumnType("longtext");

                    b.Property<DateTime?>("RedemptionDate")
                        .HasColumnType("datetime(6)");

                    b.Property<string>("ReferenceId")
                        .HasMaxLength(100)
                        .HasColumnType("varchar(100)");

                    b.Property<string>("Status")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.Property<string>("Subject")
                        .HasMaxLength(400)
                        .HasColumnType("varchar(400)");

                    b.Property<string>("Type")
                        .HasMaxLength(50)
                        .HasColumnType("varchar(50)");

                    b.HasKey("Id");

                    b.HasIndex("AuthorizationId");

                    b.HasIndex("ReferenceId");

                    b.HasIndex("ApplicationId", "Status", "Subject", "Type");

                    b.ToTable("OpenIddictTokens", (string)null);
                });

            modelBuilder.Entity("Volo.Abp.PermissionManagement.PermissionDefinitionRecord", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("char(36)");

                    b.Property<string>("DisplayName")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("varchar(256)");

                    b.Property<string>("ExtraProperties")
                        .HasColumnType("longtext")
                        .HasColumnName("ExtraProperties");

                    b.Property<string>("GroupName")
                        .IsRequired()
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<bool>("IsEnabled")
                        .HasColumnType("tinyint(1)");

                    b.Property<byte>("MultiTenancySide")
                        .HasColumnType("tinyint unsigned");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("ParentName")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("Providers")
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("StateCheckers")
                        .HasMaxLength(256)
                        .HasColumnType("varchar(256)");

                    b.HasKey("Id");

                    b.HasIndex("GroupName");

                    b.HasIndex("Name")
                        .IsUnique();

                    b.ToTable("AbpPermissions", (string)null);
                });

            modelBuilder.Entity("Volo.Abp.PermissionManagement.PermissionGrant", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("char(36)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("ProviderKey")
                        .IsRequired()
                        .HasMaxLength(72)
                        .HasColumnType("varchar(72)");

                    b.Property<string>("ProviderName")
                        .IsRequired()
                        .HasMaxLength(64)
                        .HasColumnType("varchar(64)");

                    b.Property<Guid?>("TenantId")
                        .HasColumnType("char(36)")
                        .HasColumnName("TenantId");

                    b.HasKey("Id");

                    b.HasIndex("TenantId", "Name", "ProviderName", "ProviderKey")
                        .IsUnique();

                    b.ToTable("AbpPermissionGrants", (string)null);
                });

            modelBuilder.Entity("Volo.Abp.PermissionManagement.PermissionGroupDefinitionRecord", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("char(36)");

                    b.Property<string>("DisplayName")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("varchar(256)");

                    b.Property<string>("ExtraProperties")
                        .HasColumnType("longtext")
                        .HasColumnName("ExtraProperties");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.HasKey("Id");

                    b.HasIndex("Name")
                        .IsUnique();

                    b.ToTable("AbpPermissionGroups", (string)null);
                });

            modelBuilder.Entity("Volo.Abp.SettingManagement.Setting", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("char(36)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("ProviderKey")
                        .HasMaxLength(64)
                        .HasColumnType("varchar(64)");

                    b.Property<string>("ProviderName")
                        .HasMaxLength(64)
                        .HasColumnType("varchar(64)");

                    b.Property<string>("Value")
                        .IsRequired()
                        .HasMaxLength(2048)
                        .HasColumnType("varchar(2048)");

                    b.HasKey("Id");

                    b.HasIndex("Name", "ProviderName", "ProviderKey")
                        .IsUnique();

                    b.ToTable("AbpSettings", (string)null);
                });

            modelBuilder.Entity("Volo.Abp.SettingManagement.SettingDefinitionRecord", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("char(36)");

                    b.Property<string>("DefaultValue")
                        .HasMaxLength(2048)
                        .HasColumnType("varchar(2048)");

                    b.Property<string>("Description")
                        .HasMaxLength(512)
                        .HasColumnType("varchar(512)");

                    b.Property<string>("DisplayName")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("varchar(256)");

                    b.Property<string>("ExtraProperties")
                        .HasColumnType("longtext")
                        .HasColumnName("ExtraProperties");

                    b.Property<bool>("IsEncrypted")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsInherited")
                        .HasColumnType("tinyint(1)");

                    b.Property<bool>("IsVisibleToClients")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(128)
                        .HasColumnType("varchar(128)");

                    b.Property<string>("Providers")
                        .HasMaxLength(1024)
                        .HasColumnType("varchar(1024)");

                    b.HasKey("Id");

                    b.HasIndex("Name")
                        .IsUnique();

                    b.ToTable("AbpSettingDefinitions", (string)null);
                });

            modelBuilder.Entity("Volo.Abp.TenantManagement.Tenant", b =>
                {
                    b.Property<Guid>("Id")
                        .HasColumnType("char(36)");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .IsRequired()
                        .HasMaxLength(40)
                        .HasColumnType("varchar(40)")
                        .HasColumnName("ConcurrencyStamp");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("CreationTime");

                    b.Property<Guid?>("CreatorId")
                        .HasColumnType("char(36)")
                        .HasColumnName("CreatorId");

                    b.Property<Guid?>("DeleterId")
                        .HasColumnType("char(36)")
                        .HasColumnName("DeleterId");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("DeletionTime");

                    b.Property<int>("EntityVersion")
                        .HasColumnType("int");

                    b.Property<string>("ExtraProperties")
                        .IsRequired()
                        .HasColumnType("longtext")
                        .HasColumnName("ExtraProperties");

                    b.Property<bool>("IsDeleted")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("tinyint(1)")
                        .HasDefaultValue(false)
                        .HasColumnName("IsDeleted");

                    b.Property<DateTime?>("LastModificationTime")
                        .HasColumnType("datetime(6)")
                        .HasColumnName("LastModificationTime");

                    b.Property<Guid?>("LastModifierId")
                        .HasColumnType("char(36)")
                        .HasColumnName("LastModifierId");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(64)
                        .HasColumnType("varchar(64)");

                    b.Property<string>("NormalizedName")
                        .IsRequired()
                        .HasMaxLength(64)
                        .HasColumnType("varchar(64)");

                    b.HasKey("Id");

                    b.HasIndex("Name");

                    b.HasIndex("NormalizedName");

                    b.ToTable("AbpTenants", (string)null);
                });

            modelBuilder.Entity("Volo.Abp.TenantManagement.TenantConnectionString", b =>
                {
                    b.Property<Guid>("TenantId")
                        .HasColumnType("char(36)");

                    b.Property<string>("Name")
                        .HasMaxLength(64)
                        .HasColumnType("varchar(64)");

                    b.Property<string>("Value")
                        .IsRequired()
                        .HasMaxLength(1024)
                        .HasColumnType("varchar(1024)");

                    b.HasKey("TenantId", "Name");

                    b.ToTable("AbpTenantConnectionStrings", (string)null);
                });

            modelBuilder.Entity("GoTrack.AlertDefinitions.DisassembleTrackingDevices.DisassembleTrackingDeviceAlertDefinition", b =>
                {
                    b.HasBaseType("GoTrack.AlertDefinitions.AlertDefinition");

                    b.ToTable("GoTrackDisassembleTrackingDeviceAlertDefinitions", (string)null);
                });

            modelBuilder.Entity("GoTrack.AlertDefinitions.ExceedingSpeedAlertDefinitions.ExceedingSpeedAlertDefinition", b =>
                {
                    b.HasBaseType("GoTrack.AlertDefinitions.AlertDefinition");

                    b.Property<decimal>("MaxSpeed")
                        .HasColumnType("decimal(65,30)");

                    b.ToTable("GoTrackExceedingSpeedAlertDefinitions", (string)null);
                });

            modelBuilder.Entity("GoTrack.AlertDefinitions.JobTimeAlertDefinitions.JobTimeAlertDefinition", b =>
                {
                    b.HasBaseType("GoTrack.AlertDefinitions.AlertDefinition");

                    b.Property<string>("DaysOfWeek")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<TimeOnly>("EndTime")
                        .HasColumnType("time(6)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<TimeOnly>("StartTime")
                        .HasColumnType("time(6)");

                    b.ToTable("GoTrackJobTimeAlertDefinitions", (string)null);
                });

            modelBuilder.Entity("GoTrack.AlertDefinitions.RouteAlertDefinitions.ExitingRouteAlertDefinitions.ExitingRouteAlertDefinition", b =>
                {
                    b.HasBaseType("GoTrack.AlertDefinitions.AlertDefinition");

                    b.ToTable("GoTrackExitingRouteAlertDefinitions", (string)null);
                });

            modelBuilder.Entity("GoTrack.AlertDefinitions.ZoneAlertDefinitions.EnteringZoneAlertDefinitions.EnteringZoneAlertDefinition", b =>
                {
                    b.HasBaseType("GoTrack.AlertDefinitions.AlertDefinition");

                    b.ToTable("GoTrackEnteringZoneAlertDefinitions", (string)null);
                });

            modelBuilder.Entity("GoTrack.AlertDefinitions.ZoneAlertDefinitions.ExitingZoneAlertDefinitions.ExitingZoneAlertDefinition", b =>
                {
                    b.HasBaseType("GoTrack.AlertDefinitions.AlertDefinition");

                    b.ToTable("GoTrackExitingZoneAlertDefinitions", (string)null);
                });

            modelBuilder.Entity("GoTrack.Alerts.AlertTriggers.ExceedingSpeedAlertTriggers.ExceedingSpeedAlertTrigger", b =>
                {
                    b.HasBaseType("GoTrack.Alerts.AlertTriggers.AlertTrigger");

                    b.Property<decimal>("MaxSpeed")
                        .HasColumnType("decimal(65,30)");

                    b.ToTable("GoTrackExceedingSpeedAlertTriggers", (string)null);
                });

            modelBuilder.Entity("GoTrack.Alerts.AlertTriggers.JobTimeAlertTriggers.JobTimeAlertTrigger", b =>
                {
                    b.HasBaseType("GoTrack.Alerts.AlertTriggers.AlertTrigger");

                    b.Property<TimeOnly>("EndTime")
                        .HasColumnType("time(6)");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<TimeOnly>("StartTime")
                        .HasColumnType("time(6)");

                    b.ToTable("GoTrackJobTimeAlertTriggers", (string)null);
                });

            modelBuilder.Entity("GoTrack.Alerts.AlertTriggers.RouteAlertTriggers.ExitingRouteAlertTriggers.ExitingRouteAlertTrigger", b =>
                {
                    b.HasBaseType("GoTrack.Alerts.AlertTriggers.AlertTrigger");

                    b.Property<Guid>("RouteId")
                        .HasColumnType("char(36)");

                    b.ToTable("GoTrackExitingRouteAlertTriggers", (string)null);
                });

            modelBuilder.Entity("GoTrack.Alerts.AlertTriggers.ZoneAlertTriggers.EnteringZoneAlertTriggers.EnteringZoneAlertTrigger", b =>
                {
                    b.HasBaseType("GoTrack.Alerts.AlertTriggers.AlertTrigger");

                    b.Property<Guid>("GeoZoneId")
                        .HasColumnType("char(36)");

                    b.HasIndex("GeoZoneId");

                    b.ToTable("GoTrackEnteringZoneAlertTriggers", (string)null);
                });

            modelBuilder.Entity("GoTrack.Alerts.AlertTriggers.ZoneAlertTriggers.ExitingZoneAlertTriggers.ExitingZoneAlertTrigger", b =>
                {
                    b.HasBaseType("GoTrack.Alerts.AlertTriggers.AlertTrigger");

                    b.Property<Guid>("GeoZoneId")
                        .HasColumnType("char(36)");

                    b.HasIndex("GeoZoneId");

                    b.ToTable("GoTrackExitingZoneAlertTriggers", (string)null);
                });

            modelBuilder.Entity("GoTrack.Observations.ObservationVehicle", b =>
                {
                    b.HasBaseType("GoTrack.Observations.Observation");

                    b.Property<Guid>("VehicleId")
                        .HasColumnType("char(36)");

                    b.HasIndex("VehicleId");

                    b.HasDiscriminator().HasValue(0);
                });

            modelBuilder.Entity("GoTrack.Observations.ObservationVehicleGroup", b =>
                {
                    b.HasBaseType("GoTrack.Observations.Observation");

                    b.Property<Guid>("VehicleGroupId")
                        .HasColumnType("char(36)");

                    b.HasIndex("VehicleGroupId");

                    b.HasDiscriminator().HasValue(1);
                });

            modelBuilder.Entity("GoTrack.Requests.AccountSubscriptionRequests.BusinessAccountSubscriptionRequests.BusinessAccountSubscriptionRequest", b =>
                {
                    b.HasBaseType("GoTrack.Requests.Request");

                    b.Property<string>("AccountName")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<int>("AccountSubscriptionRequestStage")
                        .HasColumnType("int");

                    b.Property<Guid?>("BillId")
                        .HasColumnType("char(36)");

                    b.Property<string>("CompanyName")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<Guid?>("CreatedTrackAccountId")
                        .HasColumnType("char(36)");

                    b.Property<string>("PromoCode")
                        .HasColumnType("longtext");

                    b.Property<Guid?>("SmsBundleId")
                        .HasColumnType("char(36)");

                    b.Property<int>("SubscriptionDurationInMonths")
                        .HasColumnType("int");

                    b.Property<string>("SubscriptionPlanKey")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<int>("TrackerInstallationLocation")
                        .HasColumnType("int");

                    b.Property<int>("UserCount")
                        .HasColumnType("int");

                    b.HasIndex("BillId");

                    b.HasIndex("SmsBundleId");

                    b.ToTable("GoTrackBusinessAccountSubscriptionRequests", (string)null);
                });

            modelBuilder.Entity("GoTrack.Requests.AccountSubscriptionRequests.PersonalAccountSubscriptionRequests.PersonalAccountSubscriptionRequest", b =>
                {
                    b.HasBaseType("GoTrack.Requests.Request");

                    b.Property<string>("AccountName")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<int>("AccountSubscriptionRequestStage")
                        .HasColumnType("int");

                    b.Property<Guid?>("BillId")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("CreatedTrackAccountId")
                        .HasColumnType("char(36)");

                    b.Property<string>("PromoCode")
                        .HasColumnType("longtext");

                    b.Property<Guid?>("SmsBundleId")
                        .HasColumnType("char(36)");

                    b.Property<int>("SubscriptionDurationInMonths")
                        .HasColumnType("int");

                    b.Property<string>("SubscriptionPlanKey")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<int>("TrackerInstallationLocation")
                        .HasColumnType("int");

                    b.Property<int>("UserCount")
                        .HasColumnType("int");

                    b.HasIndex("BillId");

                    b.HasIndex("SmsBundleId");

                    b.ToTable("GoTrackPersonalAccountSubscriptionRequests", (string)null);
                });

            modelBuilder.Entity("GoTrack.Requests.TrackAccountRequest", b =>
                {
                    b.HasBaseType("GoTrack.Requests.Request");

                    b.Property<Guid>("TrackAccountId")
                        .HasColumnType("char(36)");

                    b.ToTable("GoTrackTrackAccountRequests", (string)null);
                });

            modelBuilder.Entity("GoTrack.TrackAccounts.BusinessTrackAccounts.BusinessTrackAccount", b =>
                {
                    b.HasBaseType("GoTrack.TrackAccounts.TrackAccount");

                    b.Property<string>("CompanyName")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.ToTable("GoTrackBusinessTrackAccounts", (string)null);
                });

            modelBuilder.Entity("GoTrack.TrackAccounts.PersonalTrackAccounts.PersonalTrackAccount", b =>
                {
                    b.HasBaseType("GoTrack.TrackAccounts.TrackAccount");

                    b.ToTable("GoTrackPersonalTrackAccounts", (string)null);
                });

            modelBuilder.Entity("GoTrack.TrackableEntities.VehicleGroupTrackableEntityAssociation", b =>
                {
                    b.HasBaseType("GoTrack.TrackableEntities.TrackableEntityAssociation");

                    b.Property<Guid>("VehicleGroupId")
                        .HasColumnType("char(36)");

                    b.HasIndex("VehicleGroupId");

                    b.HasDiscriminator().HasValue(1);
                });

            modelBuilder.Entity("GoTrack.TrackableEntities.VehicleTrackableEntityAssociation", b =>
                {
                    b.HasBaseType("GoTrack.TrackableEntities.TrackableEntityAssociation");

                    b.Property<Guid>("VehicleId")
                        .HasColumnType("char(36)");

                    b.HasIndex("VehicleId");

                    b.HasDiscriminator().HasValue(0);
                });

            modelBuilder.Entity("GoTrack.Trips.TripVehicle", b =>
                {
                    b.HasBaseType("GoTrack.Trips.Trip");

                    b.Property<Guid>("VehicleId")
                        .HasColumnType("char(36)");

                    b.HasIndex("VehicleId");

                    b.HasDiscriminator().HasValue(0);
                });

            modelBuilder.Entity("GoTrack.Trips.TripVehicleGroup", b =>
                {
                    b.HasBaseType("GoTrack.Trips.Trip");

                    b.Property<Guid>("VehicleGroupId")
                        .HasColumnType("char(36)");

                    b.HasIndex("VehicleGroupId");

                    b.HasDiscriminator().HasValue(1);
                });

            modelBuilder.Entity("GoTrack.Requests.AddVehiclesRequests.AddVehiclesRequest", b =>
                {
                    b.HasBaseType("GoTrack.Requests.TrackAccountRequest");

                    b.Property<int>("AddVehiclesRequestStage")
                        .HasColumnType("int");

                    b.Property<Guid?>("BillId")
                        .HasColumnType("char(36)");

                    b.Property<bool>("HasValidDevice")
                        .HasColumnType("tinyint(1)");

                    b.Property<string>("PromoCode")
                        .HasColumnType("longtext");

                    b.Property<Guid>("TrackAccountSubscriptionId")
                        .HasColumnType("char(36)");

                    b.Property<int>("TrackerInstallationLocation")
                        .HasColumnType("int");

                    b.HasIndex("BillId");

                    b.HasIndex("TrackAccountSubscriptionId");

                    b.ToTable("GoTrackAddVehiclesRequests", (string)null);
                });

            modelBuilder.Entity("GoTrack.Requests.IncreaseUserCountRequests.IncreaseUserCountRequest", b =>
                {
                    b.HasBaseType("GoTrack.Requests.TrackAccountRequest");

                    b.Property<Guid?>("BillId")
                        .HasColumnType("char(36)");

                    b.Property<int>("IncreaseUserCountRequestStage")
                        .HasColumnType("int");

                    b.Property<Guid>("TrackAccountSubscriptionId")
                        .HasColumnType("char(36)");

                    b.Property<int>("UserCount")
                        .HasColumnType("int");

                    b.HasIndex("BillId");

                    b.HasIndex("TrackAccountSubscriptionId");

                    b.ToTable("GoTrackIncreaseUserCountRequests", (string)null);
                });

            modelBuilder.Entity("GoTrack.Requests.RenewTrackAccountSubscriptions.RenewSubscriptionRequest", b =>
                {
                    b.HasBaseType("GoTrack.Requests.TrackAccountRequest");

                    b.Property<Guid?>("BillId")
                        .HasColumnType("char(36)");

                    b.Property<Guid?>("CreatedTrackAccountSubscriptionId")
                        .HasColumnType("char(36)");

                    b.Property<string>("PromoCode")
                        .HasColumnType("longtext");

                    b.Property<string>("RemoveTrackVehicles")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<string>("RemoveUsers")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<int>("RenewSubscriptionRequestStage")
                        .HasColumnType("int");

                    b.Property<Guid?>("SmsBundleId")
                        .HasColumnType("char(36)");

                    b.Property<int>("SubscriptionDurationInMonths")
                        .HasColumnType("int");

                    b.Property<string>("SubscriptionPlanKey")
                        .IsRequired()
                        .HasColumnType("longtext");

                    b.Property<Guid>("TrackAccountSubscriptionId")
                        .HasColumnType("char(36)");

                    b.Property<int?>("TrackerInstallationLocation")
                        .HasColumnType("int");

                    b.Property<int>("UserCount")
                        .HasColumnType("int");

                    b.HasIndex("BillId");

                    b.HasIndex("SmsBundleId");

                    b.HasIndex("TrackAccountSubscriptionId");

                    b.ToTable("GoTrackRenewSubscriptionRequests", (string)null);
                });

            modelBuilder.Entity("GoTrack.Requests.SmsBundleRenewalRequests.SmsBundleRenewalRequest", b =>
                {
                    b.HasBaseType("GoTrack.Requests.TrackAccountRequest");

                    b.Property<Guid?>("BillId")
                        .HasColumnType("char(36)");

                    b.Property<Guid>("SmsBundleId")
                        .HasColumnType("char(36)");

                    b.Property<int>("SmsBundleRenewalStage")
                        .HasColumnType("int");

                    b.Property<Guid>("TrackAccountSubscriptionId")
                        .HasColumnType("char(36)");

                    b.HasIndex("BillId");

                    b.HasIndex("SmsBundleId");

                    b.HasIndex("TrackAccountSubscriptionId");

                    b.ToTable("GoTrackSmsBundleRenewalRequests", (string)null);
                });

            modelBuilder.Entity("GoTrack.AlertDefinitions.AlertDefinitionAssociations.AlertDefinitionAssociation", b =>
                {
                    b.HasOne("GoTrack.TrackableEntities.TrackableEntityAssociation", "TrackableEntityAssociation")
                        .WithMany()
                        .HasForeignKey("TrackableEntityAssociationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("TrackableEntityAssociation");
                });

            modelBuilder.Entity("GoTrack.AlertDefinitions.RouteAlertDefinitions.RouteAlertRoutes.RouteAlertRoute", b =>
                {
                    b.HasOne("GoTrack.AlertDefinitions.RouteAlertDefinitions.ExitingRouteAlertDefinitions.ExitingRouteAlertDefinition", null)
                        .WithMany("RouteAlertRoutes")
                        .HasForeignKey("ExitingRouteAlertDefinitionId");

                    b.HasOne("GoTrack.Routes.Route", "Route")
                        .WithMany()
                        .HasForeignKey("RouteId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Route");
                });

            modelBuilder.Entity("GoTrack.AlertDefinitions.ZoneAlertDefinitions.ZoneAlertGeoZones.ZoneAlertGeoZone", b =>
                {
                    b.HasOne("GoTrack.AlertDefinitions.ZoneAlertDefinitions.EnteringZoneAlertDefinitions.EnteringZoneAlertDefinition", null)
                        .WithMany("ZoneAlertGeoZones")
                        .HasForeignKey("EnteringZoneAlertDefinitionId");

                    b.HasOne("GoTrack.AlertDefinitions.ZoneAlertDefinitions.ExitingZoneAlertDefinitions.ExitingZoneAlertDefinition", null)
                        .WithMany("ZoneAlertGeoZones")
                        .HasForeignKey("ExitingZoneAlertDefinitionId");

                    b.HasOne("GoTrack.GeoZones.GeoZone", "GeoZone")
                        .WithMany()
                        .HasForeignKey("GeoZoneId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("GeoZone");
                });

            modelBuilder.Entity("GoTrack.Alerts.AlertLogs.AlertLog", b =>
                {
                    b.HasOne("GoTrack.Alerts.AlertTriggers.AlertTrigger", "AlertTrigger")
                        .WithMany()
                        .HasForeignKey("AlertTriggerId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("GoTrack.Devices.Device", "Device")
                        .WithMany()
                        .HasForeignKey("DeviceId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("GoTrack.Vehicles.Vehicle", "Vehicle")
                        .WithMany()
                        .HasForeignKey("VehicleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("AlertTrigger");

                    b.Navigation("Device");

                    b.Navigation("Vehicle");
                });

            modelBuilder.Entity("GoTrack.Alerts.AlertTriggers.AlertTrigger", b =>
                {
                    b.HasOne("GoTrack.Vehicles.Vehicle", "Vehicle")
                        .WithMany()
                        .HasForeignKey("VehicleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Vehicle");
                });

            modelBuilder.Entity("GoTrack.Devices.Device", b =>
                {
                    b.HasOne("Volo.Abp.Identity.IdentityUser", "Owner")
                        .WithMany()
                        .HasForeignKey("OwnerId");

                    b.Navigation("Owner");
                });

            modelBuilder.Entity("GoTrack.Devices.DeviceStatusLog", b =>
                {
                    b.HasOne("GoTrack.Devices.Device", "Device")
                        .WithMany("StatusLogs")
                        .HasForeignKey("DeviceId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Device");
                });

            modelBuilder.Entity("GoTrack.GeoZones.GeoZone", b =>
                {
                    b.HasOne("GoTrack.TrackAccounts.TrackAccount", "TrackAccount")
                        .WithMany()
                        .HasForeignKey("TrackAccountId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.OwnsOne("GoTrack.PolyLines.PolyLine", "Polyline", b1 =>
                        {
                            b1.Property<Guid>("GeoZoneId")
                                .HasColumnType("char(36)");

                            b1.Property<string>("Line")
                                .IsRequired()
                                .HasColumnType("longtext");

                            b1.HasKey("GeoZoneId");

                            b1.ToTable("GoTrackGeoZones");

                            b1.WithOwner()
                                .HasForeignKey("GeoZoneId");
                        });

                    b.Navigation("Polyline")
                        .IsRequired();

                    b.Navigation("TrackAccount");
                });

            modelBuilder.Entity("GoTrack.Identity.IdentityUserProfile", b =>
                {
                    b.HasOne("Volo.Abp.Identity.IdentityUser", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.OwnsOne("GoTrack.Addresses.Address", "Address", b1 =>
                        {
                            b1.Property<Guid>("IdentityUserProfileId")
                                .HasColumnType("char(36)");

                            b1.Property<string>("Area")
                                .IsRequired()
                                .HasColumnType("longtext");

                            b1.Property<string>("City")
                                .IsRequired()
                                .HasColumnType("longtext");

                            b1.Property<string>("Country")
                                .IsRequired()
                                .HasColumnType("longtext");

                            b1.Property<string>("Governorate")
                                .IsRequired()
                                .HasColumnType("longtext");

                            b1.Property<string>("Street")
                                .IsRequired()
                                .HasColumnType("longtext");

                            b1.HasKey("IdentityUserProfileId");

                            b1.ToTable("GoTrackIdentityUserProfiles");

                            b1.WithOwner()
                                .HasForeignKey("IdentityUserProfileId");
                        });

                    b.Navigation("Address");

                    b.Navigation("User");
                });

            modelBuilder.Entity("GoTrack.Observations.Observation", b =>
                {
                    b.HasOne("GoTrack.UserTrackAccountAssociations.UserTrackAccountAssociation", null)
                        .WithMany("Observations")
                        .HasForeignKey("UserTrackAccountAssociationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("GoTrack.Payments.Bills.Bill", b =>
                {
                    b.OwnsMany("GoTrack.Payments.Bills.AppliedDiscount", "AppliedDiscounts", b1 =>
                        {
                            b1.Property<Guid>("BillId")
                                .HasColumnType("char(36)");

                            b1.Property<int>("Id")
                                .ValueGeneratedOnAdd()
                                .HasColumnType("int");

                            MySqlPropertyBuilderExtensions.UseMySqlIdentityColumn(b1.Property<int>("Id"));

                            b1.Property<decimal>("Amount")
                                .HasColumnType("decimal(65,30)");

                            b1.Property<DateTime>("AppliedAt")
                                .HasColumnType("datetime(6)");

                            b1.Property<Guid>("DiscountId")
                                .HasColumnType("char(36)");

                            b1.Property<bool>("IsPercentage")
                                .HasColumnType("tinyint(1)");

                            b1.Property<string>("TargetKey")
                                .HasColumnType("longtext");

                            b1.Property<int>("TargetType")
                                .HasColumnType("int");

                            b1.Property<decimal>("Value")
                                .HasColumnType("decimal(65,30)");

                            b1.HasKey("BillId", "Id");

                            b1.ToTable("AppliedDiscount");

                            b1.WithOwner()
                                .HasForeignKey("BillId");
                        });

                    b.OwnsMany("GoTrack.Payments.Bills.BillLineItem", "BillLineItems", b1 =>
                        {
                            b1.Property<Guid>("BillId")
                                .HasColumnType("char(36)");

                            b1.Property<int>("Id")
                                .ValueGeneratedOnAdd()
                                .HasColumnType("int");

                            MySqlPropertyBuilderExtensions.UseMySqlIdentityColumn(b1.Property<int>("Id"));

                            b1.Property<decimal>("BillableAmount")
                                .HasColumnType("decimal(65,30)");

                            b1.Property<string>("PricingItemKey")
                                .IsRequired()
                                .HasColumnType("longtext");

                            b1.Property<int>("PricingType")
                                .HasColumnType("int");

                            b1.Property<int>("Quantity")
                                .HasColumnType("int");

                            b1.Property<int?>("RequestedMonths")
                                .HasColumnType("int");

                            b1.Property<decimal>("UnitPrice")
                                .HasColumnType("decimal(65,30)");

                            b1.HasKey("BillId", "Id");

                            b1.ToTable("BillLineItem");

                            b1.WithOwner()
                                .HasForeignKey("BillId");
                        });

                    b.Navigation("AppliedDiscounts");

                    b.Navigation("BillLineItems");
                });

            modelBuilder.Entity("GoTrack.Payments.Discounts.Discount", b =>
                {
                    b.OwnsMany("GoTrack.Payments.Discounts.DiscountCriteria", "DiscountCriteriaList", b1 =>
                        {
                            b1.Property<Guid>("DiscountId")
                                .HasColumnType("char(36)");

                            b1.Property<int>("Id")
                                .ValueGeneratedOnAdd()
                                .HasColumnType("int");

                            MySqlPropertyBuilderExtensions.UseMySqlIdentityColumn(b1.Property<int>("Id"));

                            b1.Property<int>("DiscountSpecificationKey")
                                .HasColumnType("int");

                            b1.Property<string>("SpecificationValue")
                                .IsRequired()
                                .HasColumnType("longtext");

                            b1.HasKey("DiscountId", "Id");

                            b1.ToTable("DiscountCriteria");

                            b1.WithOwner()
                                .HasForeignKey("DiscountId");
                        });

                    b.Navigation("DiscountCriteriaList");
                });

            modelBuilder.Entity("GoTrack.Payments.PricingItems.PricingItem", b =>
                {
                    b.OwnsMany("GoTrack.Payments.PricingItems.Price", "PriceHistory", b1 =>
                        {
                            b1.Property<Guid>("PricingItemId")
                                .HasColumnType("char(36)");

                            b1.Property<int>("Id")
                                .ValueGeneratedOnAdd()
                                .HasColumnType("int");

                            MySqlPropertyBuilderExtensions.UseMySqlIdentityColumn(b1.Property<int>("Id"));

                            b1.Property<decimal>("Amount")
                                .HasColumnType("decimal(65,30)");

                            b1.Property<DateTime>("EffectiveDate")
                                .HasColumnType("datetime(6)");

                            b1.HasKey("PricingItemId", "Id");

                            b1.ToTable("Price");

                            b1.WithOwner()
                                .HasForeignKey("PricingItemId");
                        });

                    b.Navigation("PriceHistory");
                });

            modelBuilder.Entity("GoTrack.Payments.PromoCodes.PromoCode", b =>
                {
                    b.HasOne("GoTrack.Payments.Discounts.Discount", "Discount")
                        .WithMany()
                        .HasForeignKey("DiscountId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Discount");
                });

            modelBuilder.Entity("GoTrack.Requests.Request", b =>
                {
                    b.HasOne("Volo.Abp.Identity.IdentityUser", "Owner")
                        .WithMany()
                        .HasForeignKey("OwnerId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Owner");
                });

            modelBuilder.Entity("GoTrack.Requests.RequestNote", b =>
                {
                    b.HasOne("GoTrack.Requests.Request", null)
                        .WithMany("Notes")
                        .HasForeignKey("RequestId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("GoTrack.Routes.Route", b =>
                {
                    b.OwnsMany("GoTrack.Routes.RouteStopPoint", "StopPoints", b1 =>
                        {
                            b1.Property<Guid>("RouteId")
                                .HasColumnType("char(36)");

                            b1.Property<int>("Id")
                                .ValueGeneratedOnAdd()
                                .HasColumnType("int");

                            MySqlPropertyBuilderExtensions.UseMySqlIdentityColumn(b1.Property<int>("Id"));

                            b1.Property<Guid>("StopPointId")
                                .HasColumnType("char(36)");

                            b1.HasKey("RouteId", "Id");

                            b1.ToTable("RouteStopPoint");

                            b1.WithOwner()
                                .HasForeignKey("RouteId");
                        });

                    b.Navigation("StopPoints");
                });

            modelBuilder.Entity("GoTrack.TrackAccounts.TrackAccountSubscriptions.TrackAccountSubscription", b =>
                {
                    b.HasOne("GoTrack.TrackAccounts.TrackAccount", "TrackAccount")
                        .WithMany("TrackAccountSubscriptions")
                        .HasForeignKey("TrackAccountId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("TrackAccount");
                });

            modelBuilder.Entity("GoTrack.TripTemplates.TripTemplate", b =>
                {
                    b.OwnsMany("GoTrack.TripTemplates.TripTemplateRoute", "TripTemplateRoutes", b1 =>
                        {
                            b1.Property<Guid>("TripTemplateId")
                                .HasColumnType("char(36)");

                            b1.Property<int>("Id")
                                .ValueGeneratedOnAdd()
                                .HasColumnType("int");

                            MySqlPropertyBuilderExtensions.UseMySqlIdentityColumn(b1.Property<int>("Id"));

                            b1.Property<Guid>("RouteId")
                                .HasColumnType("char(36)");

                            b1.HasKey("TripTemplateId", "Id");

                            b1.ToTable("TripTemplateRoute");

                            b1.WithOwner()
                                .HasForeignKey("TripTemplateId");
                        });

                    b.Navigation("TripTemplateRoutes");
                });

            modelBuilder.Entity("GoTrack.UserTrackAccountAssociations.UserTrackAccountAssociation", b =>
                {
                    b.HasOne("GoTrack.TrackAccounts.TrackAccount", null)
                        .WithMany("UserTrackAccountAssociations")
                        .HasForeignKey("TrackAccountId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("GoTrack.VehicleDeviceEventLogs.VehicleDeviceEventLog", b =>
                {
                    b.HasOne("GoTrack.Devices.Device", "Device")
                        .WithMany()
                        .HasForeignKey("DeviceId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("GoTrack.Vehicles.Vehicle", "Vehicle")
                        .WithMany()
                        .HasForeignKey("VehicleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Device");

                    b.Navigation("Vehicle");
                });

            modelBuilder.Entity("GoTrack.Vehicles.Vehicle", b =>
                {
                    b.HasOne("GoTrack.TrackAccounts.TrackAccount", "TrackAccount")
                        .WithMany()
                        .HasForeignKey("TrackAccountId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.OwnsOne("GoTrack.Vehicles.LicensePlates.LicensePlate", "LicensePlate", b1 =>
                        {
                            b1.Property<Guid>("VehicleId")
                                .HasColumnType("char(36)");

                            b1.Property<string>("Serial")
                                .IsRequired()
                                .HasColumnType("longtext");

                            b1.Property<int>("SubClass")
                                .HasColumnType("int");

                            b1.HasKey("VehicleId");

                            b1.ToTable("GoTrackVehicles");

                            b1.WithOwner()
                                .HasForeignKey("VehicleId");
                        });

                    b.Navigation("LicensePlate")
                        .IsRequired();

                    b.Navigation("TrackAccount");
                });

            modelBuilder.Entity("GoTrack.Vehicles.VehicleGroupVehicle", b =>
                {
                    b.HasOne("GoTrack.VehicleGroups.VehicleGroup", "VehicleGroup")
                        .WithMany("VehicleGroupVehicles")
                        .HasForeignKey("VehicleGroupId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("GoTrack.Vehicles.Vehicle", "Vehicle")
                        .WithMany("VehicleGroupVehicles")
                        .HasForeignKey("VehicleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Vehicle");

                    b.Navigation("VehicleGroup");
                });

            modelBuilder.Entity("Volo.Abp.AuditLogging.AuditLogAction", b =>
                {
                    b.HasOne("Volo.Abp.AuditLogging.AuditLog", null)
                        .WithMany("Actions")
                        .HasForeignKey("AuditLogId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Volo.Abp.AuditLogging.EntityChange", b =>
                {
                    b.HasOne("Volo.Abp.AuditLogging.AuditLog", null)
                        .WithMany("EntityChanges")
                        .HasForeignKey("AuditLogId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Volo.Abp.AuditLogging.EntityPropertyChange", b =>
                {
                    b.HasOne("Volo.Abp.AuditLogging.EntityChange", null)
                        .WithMany("PropertyChanges")
                        .HasForeignKey("EntityChangeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Volo.Abp.BlobStoring.Database.DatabaseBlob", b =>
                {
                    b.HasOne("Volo.Abp.BlobStoring.Database.DatabaseBlobContainer", null)
                        .WithMany()
                        .HasForeignKey("ContainerId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Volo.Abp.Identity.IdentityRoleClaim", b =>
                {
                    b.HasOne("Volo.Abp.Identity.IdentityRole", null)
                        .WithMany("Claims")
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Volo.Abp.Identity.IdentityUserClaim", b =>
                {
                    b.HasOne("Volo.Abp.Identity.IdentityUser", null)
                        .WithMany("Claims")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Volo.Abp.Identity.IdentityUserLogin", b =>
                {
                    b.HasOne("Volo.Abp.Identity.IdentityUser", null)
                        .WithMany("Logins")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Volo.Abp.Identity.IdentityUserOrganizationUnit", b =>
                {
                    b.HasOne("Volo.Abp.Identity.OrganizationUnit", null)
                        .WithMany()
                        .HasForeignKey("OrganizationUnitId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Volo.Abp.Identity.IdentityUser", null)
                        .WithMany("OrganizationUnits")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Volo.Abp.Identity.IdentityUserRole", b =>
                {
                    b.HasOne("Volo.Abp.Identity.IdentityRole", null)
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Volo.Abp.Identity.IdentityUser", null)
                        .WithMany("Roles")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Volo.Abp.Identity.IdentityUserToken", b =>
                {
                    b.HasOne("Volo.Abp.Identity.IdentityUser", null)
                        .WithMany("Tokens")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Volo.Abp.Identity.OrganizationUnit", b =>
                {
                    b.HasOne("Volo.Abp.Identity.OrganizationUnit", null)
                        .WithMany()
                        .HasForeignKey("ParentId");
                });

            modelBuilder.Entity("Volo.Abp.Identity.OrganizationUnitRole", b =>
                {
                    b.HasOne("Volo.Abp.Identity.OrganizationUnit", null)
                        .WithMany("Roles")
                        .HasForeignKey("OrganizationUnitId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Volo.Abp.Identity.IdentityRole", null)
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Volo.Abp.OpenIddict.Authorizations.OpenIddictAuthorization", b =>
                {
                    b.HasOne("Volo.Abp.OpenIddict.Applications.OpenIddictApplication", null)
                        .WithMany()
                        .HasForeignKey("ApplicationId");
                });

            modelBuilder.Entity("Volo.Abp.OpenIddict.Tokens.OpenIddictToken", b =>
                {
                    b.HasOne("Volo.Abp.OpenIddict.Applications.OpenIddictApplication", null)
                        .WithMany()
                        .HasForeignKey("ApplicationId");

                    b.HasOne("Volo.Abp.OpenIddict.Authorizations.OpenIddictAuthorization", null)
                        .WithMany()
                        .HasForeignKey("AuthorizationId");
                });

            modelBuilder.Entity("Volo.Abp.TenantManagement.TenantConnectionString", b =>
                {
                    b.HasOne("Volo.Abp.TenantManagement.Tenant", null)
                        .WithMany("ConnectionStrings")
                        .HasForeignKey("TenantId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("GoTrack.AlertDefinitions.DisassembleTrackingDevices.DisassembleTrackingDeviceAlertDefinition", b =>
                {
                    b.HasOne("GoTrack.AlertDefinitions.AlertDefinition", null)
                        .WithOne()
                        .HasForeignKey("GoTrack.AlertDefinitions.DisassembleTrackingDevices.DisassembleTrackingDeviceAlertDefinition", "Id")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("GoTrack.AlertDefinitions.ExceedingSpeedAlertDefinitions.ExceedingSpeedAlertDefinition", b =>
                {
                    b.HasOne("GoTrack.AlertDefinitions.AlertDefinition", null)
                        .WithOne()
                        .HasForeignKey("GoTrack.AlertDefinitions.ExceedingSpeedAlertDefinitions.ExceedingSpeedAlertDefinition", "Id")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("GoTrack.AlertDefinitions.JobTimeAlertDefinitions.JobTimeAlertDefinition", b =>
                {
                    b.HasOne("GoTrack.AlertDefinitions.AlertDefinition", null)
                        .WithOne()
                        .HasForeignKey("GoTrack.AlertDefinitions.JobTimeAlertDefinitions.JobTimeAlertDefinition", "Id")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("GoTrack.AlertDefinitions.RouteAlertDefinitions.ExitingRouteAlertDefinitions.ExitingRouteAlertDefinition", b =>
                {
                    b.HasOne("GoTrack.AlertDefinitions.AlertDefinition", null)
                        .WithOne()
                        .HasForeignKey("GoTrack.AlertDefinitions.RouteAlertDefinitions.ExitingRouteAlertDefinitions.ExitingRouteAlertDefinition", "Id")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("GoTrack.AlertDefinitions.ZoneAlertDefinitions.EnteringZoneAlertDefinitions.EnteringZoneAlertDefinition", b =>
                {
                    b.HasOne("GoTrack.AlertDefinitions.AlertDefinition", null)
                        .WithOne()
                        .HasForeignKey("GoTrack.AlertDefinitions.ZoneAlertDefinitions.EnteringZoneAlertDefinitions.EnteringZoneAlertDefinition", "Id")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("GoTrack.AlertDefinitions.ZoneAlertDefinitions.ExitingZoneAlertDefinitions.ExitingZoneAlertDefinition", b =>
                {
                    b.HasOne("GoTrack.AlertDefinitions.AlertDefinition", null)
                        .WithOne()
                        .HasForeignKey("GoTrack.AlertDefinitions.ZoneAlertDefinitions.ExitingZoneAlertDefinitions.ExitingZoneAlertDefinition", "Id")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("GoTrack.Alerts.AlertTriggers.ExceedingSpeedAlertTriggers.ExceedingSpeedAlertTrigger", b =>
                {
                    b.HasOne("GoTrack.Alerts.AlertTriggers.AlertTrigger", null)
                        .WithOne()
                        .HasForeignKey("GoTrack.Alerts.AlertTriggers.ExceedingSpeedAlertTriggers.ExceedingSpeedAlertTrigger", "Id")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("GoTrack.Alerts.AlertTriggers.JobTimeAlertTriggers.JobTimeAlertTrigger", b =>
                {
                    b.HasOne("GoTrack.Alerts.AlertTriggers.AlertTrigger", null)
                        .WithOne()
                        .HasForeignKey("GoTrack.Alerts.AlertTriggers.JobTimeAlertTriggers.JobTimeAlertTrigger", "Id")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("GoTrack.Alerts.AlertTriggers.RouteAlertTriggers.ExitingRouteAlertTriggers.ExitingRouteAlertTrigger", b =>
                {
                    b.HasOne("GoTrack.Alerts.AlertTriggers.AlertTrigger", null)
                        .WithOne()
                        .HasForeignKey("GoTrack.Alerts.AlertTriggers.RouteAlertTriggers.ExitingRouteAlertTriggers.ExitingRouteAlertTrigger", "Id")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("GoTrack.Alerts.AlertTriggers.ZoneAlertTriggers.EnteringZoneAlertTriggers.EnteringZoneAlertTrigger", b =>
                {
                    b.HasOne("GoTrack.GeoZones.GeoZone", "GeoZone")
                        .WithMany()
                        .HasForeignKey("GeoZoneId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("GoTrack.Alerts.AlertTriggers.AlertTrigger", null)
                        .WithOne()
                        .HasForeignKey("GoTrack.Alerts.AlertTriggers.ZoneAlertTriggers.EnteringZoneAlertTriggers.EnteringZoneAlertTrigger", "Id")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("GeoZone");
                });

            modelBuilder.Entity("GoTrack.Alerts.AlertTriggers.ZoneAlertTriggers.ExitingZoneAlertTriggers.ExitingZoneAlertTrigger", b =>
                {
                    b.HasOne("GoTrack.GeoZones.GeoZone", "GeoZone")
                        .WithMany()
                        .HasForeignKey("GeoZoneId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("GoTrack.Alerts.AlertTriggers.AlertTrigger", null)
                        .WithOne()
                        .HasForeignKey("GoTrack.Alerts.AlertTriggers.ZoneAlertTriggers.ExitingZoneAlertTriggers.ExitingZoneAlertTrigger", "Id")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("GeoZone");
                });

            modelBuilder.Entity("GoTrack.Observations.ObservationVehicle", b =>
                {
                    b.HasOne("GoTrack.Vehicles.Vehicle", "Vehicle")
                        .WithMany()
                        .HasForeignKey("VehicleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Vehicle");
                });

            modelBuilder.Entity("GoTrack.Observations.ObservationVehicleGroup", b =>
                {
                    b.HasOne("GoTrack.VehicleGroups.VehicleGroup", "VehicleGroup")
                        .WithMany()
                        .HasForeignKey("VehicleGroupId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("VehicleGroup");
                });

            modelBuilder.Entity("GoTrack.Requests.AccountSubscriptionRequests.BusinessAccountSubscriptionRequests.BusinessAccountSubscriptionRequest", b =>
                {
                    b.HasOne("GoTrack.Payments.Bills.Bill", "Bill")
                        .WithMany()
                        .HasForeignKey("BillId");

                    b.HasOne("GoTrack.Requests.Request", null)
                        .WithOne()
                        .HasForeignKey("GoTrack.Requests.AccountSubscriptionRequests.BusinessAccountSubscriptionRequests.BusinessAccountSubscriptionRequest", "Id")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("GoTrack.SmsBundles.SmsBundle", "SmsBundle")
                        .WithMany()
                        .HasForeignKey("SmsBundleId");

                    b.OwnsOne("GoTrack.Addresses.Address", "CompanyAddress", b1 =>
                        {
                            b1.Property<Guid>("BusinessAccountSubscriptionRequestId")
                                .HasColumnType("char(36)");

                            b1.Property<string>("Area")
                                .IsRequired()
                                .HasColumnType("longtext");

                            b1.Property<string>("City")
                                .IsRequired()
                                .HasColumnType("longtext");

                            b1.Property<string>("Country")
                                .IsRequired()
                                .HasColumnType("longtext");

                            b1.Property<string>("Governorate")
                                .IsRequired()
                                .HasColumnType("longtext");

                            b1.Property<string>("Street")
                                .IsRequired()
                                .HasColumnType("longtext");

                            b1.HasKey("BusinessAccountSubscriptionRequestId");

                            b1.ToTable("GoTrackBusinessAccountSubscriptionRequests");

                            b1.WithOwner()
                                .HasForeignKey("BusinessAccountSubscriptionRequestId");
                        });

                    b.OwnsMany("GoTrack.Requests.AccountSubscriptionRequests.SubscriptionVehicleInfo", "TrackVehicles", b1 =>
                        {
                            b1.Property<Guid>("BusinessAccountSubscriptionRequestId")
                                .HasColumnType("char(36)");

                            b1.Property<int>("Id")
                                .ValueGeneratedOnAdd()
                                .HasColumnType("int");

                            MySqlPropertyBuilderExtensions.UseMySqlIdentityColumn(b1.Property<int>("Id"));

                            b1.Property<int>("Color")
                                .HasColumnType("int");

                            b1.Property<double>("ConsumptionRate")
                                .HasColumnType("double");

                            b1.Property<Guid?>("DeviceId")
                                .HasColumnType("char(36)");

                            b1.Property<bool>("NeedsTrackingDevice")
                                .HasColumnType("tinyint(1)");

                            b1.HasKey("BusinessAccountSubscriptionRequestId", "Id");

                            b1.HasIndex("DeviceId");

                            b1.ToTable("GoTrackBusinessAccountSubscriptionRequestTrackVehicles", (string)null);

                            b1.WithOwner()
                                .HasForeignKey("BusinessAccountSubscriptionRequestId");

                            b1.HasOne("GoTrack.Devices.Device", "Device")
                                .WithMany()
                                .HasForeignKey("DeviceId")
                                .HasConstraintName("FK_GoTrackBusinessAccountSubscriptionRequestTrackVehicles_GoTr~1");

                            b1.OwnsOne("GoTrack.Requests.AccountSubscriptionRequests.SubscriptionVehicleLicensePlate", "LicensePlate", b2 =>
                                {
                                    b2.Property<Guid>("SubscriptionVehicleInfoBusinessAccountSubscriptionRequestId")
                                        .HasColumnType("char(36)");

                                    b2.Property<int>("SubscriptionVehicleInfoId")
                                        .HasColumnType("int");

                                    b2.Property<string>("Serial")
                                        .IsRequired()
                                        .HasColumnType("longtext");

                                    b2.Property<int>("SubClass")
                                        .HasColumnType("int");

                                    b2.HasKey("SubscriptionVehicleInfoBusinessAccountSubscriptionRequestId", "SubscriptionVehicleInfoId");

                                    b2.ToTable("GoTrackBusinessAccountSubscriptionRequestTrackVehicles");

                                    b2.WithOwner()
                                        .HasForeignKey("SubscriptionVehicleInfoBusinessAccountSubscriptionRequestId", "SubscriptionVehicleInfoId");
                                });

                            b1.Navigation("Device");

                            b1.Navigation("LicensePlate")
                                .IsRequired();
                        });

                    b.Navigation("Bill");

                    b.Navigation("CompanyAddress")
                        .IsRequired();

                    b.Navigation("SmsBundle");

                    b.Navigation("TrackVehicles");
                });

            modelBuilder.Entity("GoTrack.Requests.AccountSubscriptionRequests.PersonalAccountSubscriptionRequests.PersonalAccountSubscriptionRequest", b =>
                {
                    b.HasOne("GoTrack.Payments.Bills.Bill", "Bill")
                        .WithMany()
                        .HasForeignKey("BillId");

                    b.HasOne("GoTrack.Requests.Request", null)
                        .WithOne()
                        .HasForeignKey("GoTrack.Requests.AccountSubscriptionRequests.PersonalAccountSubscriptionRequests.PersonalAccountSubscriptionRequest", "Id")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("GoTrack.SmsBundles.SmsBundle", "SmsBundle")
                        .WithMany()
                        .HasForeignKey("SmsBundleId");

                    b.OwnsOne("GoTrack.Addresses.Address", "Address", b1 =>
                        {
                            b1.Property<Guid>("PersonalAccountSubscriptionRequestId")
                                .HasColumnType("char(36)");

                            b1.Property<string>("Area")
                                .IsRequired()
                                .HasColumnType("longtext");

                            b1.Property<string>("City")
                                .IsRequired()
                                .HasColumnType("longtext");

                            b1.Property<string>("Country")
                                .IsRequired()
                                .HasColumnType("longtext");

                            b1.Property<string>("Governorate")
                                .IsRequired()
                                .HasColumnType("longtext");

                            b1.Property<string>("Street")
                                .IsRequired()
                                .HasColumnType("longtext");

                            b1.HasKey("PersonalAccountSubscriptionRequestId");

                            b1.ToTable("GoTrackPersonalAccountSubscriptionRequests");

                            b1.WithOwner()
                                .HasForeignKey("PersonalAccountSubscriptionRequestId");
                        });

                    b.OwnsMany("GoTrack.Requests.AccountSubscriptionRequests.SubscriptionVehicleInfo", "TrackVehicles", b1 =>
                        {
                            b1.Property<Guid>("PersonalAccountSubscriptionRequestId")
                                .HasColumnType("char(36)");

                            b1.Property<int>("Id")
                                .ValueGeneratedOnAdd()
                                .HasColumnType("int");

                            MySqlPropertyBuilderExtensions.UseMySqlIdentityColumn(b1.Property<int>("Id"));

                            b1.Property<int>("Color")
                                .HasColumnType("int");

                            b1.Property<double>("ConsumptionRate")
                                .HasColumnType("double");

                            b1.Property<Guid?>("DeviceId")
                                .HasColumnType("char(36)");

                            b1.Property<bool>("NeedsTrackingDevice")
                                .HasColumnType("tinyint(1)");

                            b1.HasKey("PersonalAccountSubscriptionRequestId", "Id");

                            b1.HasIndex("DeviceId");

                            b1.ToTable("GoTrackPersonalAccountSubscriptionRequestTrackVehicles", (string)null);

                            b1.HasOne("GoTrack.Devices.Device", "Device")
                                .WithMany()
                                .HasForeignKey("DeviceId");

                            b1.WithOwner()
                                .HasForeignKey("PersonalAccountSubscriptionRequestId")
                                .HasConstraintName("FK_GoTrackPersonalAccountSubscriptionRequestTrackVehicles_GoTr~1");

                            b1.OwnsOne("GoTrack.Requests.AccountSubscriptionRequests.SubscriptionVehicleLicensePlate", "LicensePlate", b2 =>
                                {
                                    b2.Property<Guid>("SubscriptionVehicleInfoPersonalAccountSubscriptionRequestId")
                                        .HasColumnType("char(36)");

                                    b2.Property<int>("SubscriptionVehicleInfoId")
                                        .HasColumnType("int");

                                    b2.Property<string>("Serial")
                                        .IsRequired()
                                        .HasColumnType("longtext");

                                    b2.Property<int>("SubClass")
                                        .HasColumnType("int");

                                    b2.HasKey("SubscriptionVehicleInfoPersonalAccountSubscriptionRequestId", "SubscriptionVehicleInfoId");

                                    b2.ToTable("GoTrackPersonalAccountSubscriptionRequestTrackVehicles");

                                    b2.WithOwner()
                                        .HasForeignKey("SubscriptionVehicleInfoPersonalAccountSubscriptionRequestId", "SubscriptionVehicleInfoId");
                                });

                            b1.Navigation("Device");

                            b1.Navigation("LicensePlate")
                                .IsRequired();
                        });

                    b.Navigation("Address");

                    b.Navigation("Bill");

                    b.Navigation("SmsBundle");

                    b.Navigation("TrackVehicles");
                });

            modelBuilder.Entity("GoTrack.Requests.TrackAccountRequest", b =>
                {
                    b.HasOne("GoTrack.Requests.Request", null)
                        .WithOne()
                        .HasForeignKey("GoTrack.Requests.TrackAccountRequest", "Id")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("GoTrack.TrackAccounts.BusinessTrackAccounts.BusinessTrackAccount", b =>
                {
                    b.HasOne("GoTrack.TrackAccounts.TrackAccount", null)
                        .WithOne()
                        .HasForeignKey("GoTrack.TrackAccounts.BusinessTrackAccounts.BusinessTrackAccount", "Id")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.OwnsOne("GoTrack.Addresses.Address", "CompanyAddress", b1 =>
                        {
                            b1.Property<Guid>("BusinessTrackAccountId")
                                .HasColumnType("char(36)");

                            b1.Property<string>("Area")
                                .IsRequired()
                                .HasColumnType("longtext");

                            b1.Property<string>("City")
                                .IsRequired()
                                .HasColumnType("longtext");

                            b1.Property<string>("Country")
                                .IsRequired()
                                .HasColumnType("longtext");

                            b1.Property<string>("Governorate")
                                .IsRequired()
                                .HasColumnType("longtext");

                            b1.Property<string>("Street")
                                .IsRequired()
                                .HasColumnType("longtext");

                            b1.HasKey("BusinessTrackAccountId");

                            b1.ToTable("GoTrackBusinessTrackAccounts");

                            b1.WithOwner()
                                .HasForeignKey("BusinessTrackAccountId");
                        });

                    b.Navigation("CompanyAddress")
                        .IsRequired();
                });

            modelBuilder.Entity("GoTrack.TrackAccounts.PersonalTrackAccounts.PersonalTrackAccount", b =>
                {
                    b.HasOne("GoTrack.TrackAccounts.TrackAccount", null)
                        .WithOne()
                        .HasForeignKey("GoTrack.TrackAccounts.PersonalTrackAccounts.PersonalTrackAccount", "Id")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("GoTrack.TrackableEntities.VehicleGroupTrackableEntityAssociation", b =>
                {
                    b.HasOne("GoTrack.VehicleGroups.VehicleGroup", "VehicleGroup")
                        .WithMany()
                        .HasForeignKey("VehicleGroupId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("VehicleGroup");
                });

            modelBuilder.Entity("GoTrack.TrackableEntities.VehicleTrackableEntityAssociation", b =>
                {
                    b.HasOne("GoTrack.Vehicles.Vehicle", "Vehicle")
                        .WithMany()
                        .HasForeignKey("VehicleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Vehicle");
                });

            modelBuilder.Entity("GoTrack.Trips.TripVehicle", b =>
                {
                    b.HasOne("GoTrack.Vehicles.Vehicle", "Vehicle")
                        .WithMany()
                        .HasForeignKey("VehicleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Vehicle");
                });

            modelBuilder.Entity("GoTrack.Trips.TripVehicleGroup", b =>
                {
                    b.HasOne("GoTrack.VehicleGroups.VehicleGroup", "VehicleGroup")
                        .WithMany()
                        .HasForeignKey("VehicleGroupId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("VehicleGroup");
                });

            modelBuilder.Entity("GoTrack.Requests.AddVehiclesRequests.AddVehiclesRequest", b =>
                {
                    b.HasOne("GoTrack.Payments.Bills.Bill", "Bill")
                        .WithMany()
                        .HasForeignKey("BillId");

                    b.HasOne("GoTrack.Requests.TrackAccountRequest", null)
                        .WithOne()
                        .HasForeignKey("GoTrack.Requests.AddVehiclesRequests.AddVehiclesRequest", "Id")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("GoTrack.TrackAccounts.TrackAccountSubscriptions.TrackAccountSubscription", "TrackAccountSubscription")
                        .WithMany()
                        .HasForeignKey("TrackAccountSubscriptionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.OwnsMany("GoTrack.Requests.AccountSubscriptionRequests.SubscriptionVehicleInfo", "TrackVehicles", b1 =>
                        {
                            b1.Property<Guid>("AddVehiclesRequestId")
                                .HasColumnType("char(36)");

                            b1.Property<int>("Id")
                                .ValueGeneratedOnAdd()
                                .HasColumnType("int");

                            MySqlPropertyBuilderExtensions.UseMySqlIdentityColumn(b1.Property<int>("Id"));

                            b1.Property<int>("Color")
                                .HasColumnType("int");

                            b1.Property<double>("ConsumptionRate")
                                .HasColumnType("double");

                            b1.Property<Guid?>("DeviceId")
                                .HasColumnType("char(36)");

                            b1.Property<bool>("NeedsTrackingDevice")
                                .HasColumnType("tinyint(1)");

                            b1.HasKey("AddVehiclesRequestId", "Id");

                            b1.HasIndex("DeviceId");

                            b1.ToTable("GoTrackAddVehiclesRequestTrackVehicles", (string)null);

                            b1.WithOwner()
                                .HasForeignKey("AddVehiclesRequestId");

                            b1.HasOne("GoTrack.Devices.Device", "Device")
                                .WithMany()
                                .HasForeignKey("DeviceId");

                            b1.OwnsOne("GoTrack.Requests.AccountSubscriptionRequests.SubscriptionVehicleLicensePlate", "LicensePlate", b2 =>
                                {
                                    b2.Property<Guid>("SubscriptionVehicleInfoAddVehiclesRequestId")
                                        .HasColumnType("char(36)");

                                    b2.Property<int>("SubscriptionVehicleInfoId")
                                        .HasColumnType("int");

                                    b2.Property<string>("Serial")
                                        .IsRequired()
                                        .HasColumnType("longtext");

                                    b2.Property<int>("SubClass")
                                        .HasColumnType("int");

                                    b2.HasKey("SubscriptionVehicleInfoAddVehiclesRequestId", "SubscriptionVehicleInfoId");

                                    b2.ToTable("GoTrackAddVehiclesRequestTrackVehicles");

                                    b2.WithOwner()
                                        .HasForeignKey("SubscriptionVehicleInfoAddVehiclesRequestId", "SubscriptionVehicleInfoId");
                                });

                            b1.Navigation("Device");

                            b1.Navigation("LicensePlate")
                                .IsRequired();
                        });

                    b.Navigation("Bill");

                    b.Navigation("TrackAccountSubscription");

                    b.Navigation("TrackVehicles");
                });

            modelBuilder.Entity("GoTrack.Requests.IncreaseUserCountRequests.IncreaseUserCountRequest", b =>
                {
                    b.HasOne("GoTrack.Payments.Bills.Bill", "Bill")
                        .WithMany()
                        .HasForeignKey("BillId");

                    b.HasOne("GoTrack.Requests.TrackAccountRequest", null)
                        .WithOne()
                        .HasForeignKey("GoTrack.Requests.IncreaseUserCountRequests.IncreaseUserCountRequest", "Id")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("GoTrack.TrackAccounts.TrackAccountSubscriptions.TrackAccountSubscription", "TrackAccountSubscription")
                        .WithMany()
                        .HasForeignKey("TrackAccountSubscriptionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Bill");

                    b.Navigation("TrackAccountSubscription");
                });

            modelBuilder.Entity("GoTrack.Requests.RenewTrackAccountSubscriptions.RenewSubscriptionRequest", b =>
                {
                    b.HasOne("GoTrack.Payments.Bills.Bill", "Bill")
                        .WithMany()
                        .HasForeignKey("BillId");

                    b.HasOne("GoTrack.Requests.TrackAccountRequest", null)
                        .WithOne()
                        .HasForeignKey("GoTrack.Requests.RenewTrackAccountSubscriptions.RenewSubscriptionRequest", "Id")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("GoTrack.SmsBundles.SmsBundle", "SmsBundle")
                        .WithMany()
                        .HasForeignKey("SmsBundleId");

                    b.HasOne("GoTrack.TrackAccounts.TrackAccountSubscriptions.TrackAccountSubscription", "TrackAccountSubscription")
                        .WithMany()
                        .HasForeignKey("TrackAccountSubscriptionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.OwnsMany("GoTrack.Requests.AccountSubscriptionRequests.SubscriptionVehicleInfo", "NewTrackVehicles", b1 =>
                        {
                            b1.Property<Guid>("RenewSubscriptionRequestId")
                                .HasColumnType("char(36)");

                            b1.Property<int>("Id")
                                .ValueGeneratedOnAdd()
                                .HasColumnType("int");

                            MySqlPropertyBuilderExtensions.UseMySqlIdentityColumn(b1.Property<int>("Id"));

                            b1.Property<int>("Color")
                                .HasColumnType("int");

                            b1.Property<double>("ConsumptionRate")
                                .HasColumnType("double");

                            b1.Property<Guid?>("DeviceId")
                                .HasColumnType("char(36)");

                            b1.Property<bool>("NeedsTrackingDevice")
                                .HasColumnType("tinyint(1)");

                            b1.HasKey("RenewSubscriptionRequestId", "Id");

                            b1.HasIndex("DeviceId");

                            b1.ToTable("GoTrackNewSubscriptionRequestTrackVehicles", (string)null);

                            b1.HasOne("GoTrack.Devices.Device", "Device")
                                .WithMany()
                                .HasForeignKey("DeviceId");

                            b1.WithOwner()
                                .HasForeignKey("RenewSubscriptionRequestId");

                            b1.OwnsOne("GoTrack.Requests.AccountSubscriptionRequests.SubscriptionVehicleLicensePlate", "LicensePlate", b2 =>
                                {
                                    b2.Property<Guid>("SubscriptionVehicleInfoRenewSubscriptionRequestId")
                                        .HasColumnType("char(36)");

                                    b2.Property<int>("SubscriptionVehicleInfoId")
                                        .HasColumnType("int");

                                    b2.Property<string>("Serial")
                                        .IsRequired()
                                        .HasColumnType("longtext");

                                    b2.Property<int>("SubClass")
                                        .HasColumnType("int");

                                    b2.HasKey("SubscriptionVehicleInfoRenewSubscriptionRequestId", "SubscriptionVehicleInfoId");

                                    b2.ToTable("GoTrackNewSubscriptionRequestTrackVehicles");

                                    b2.WithOwner()
                                        .HasForeignKey("SubscriptionVehicleInfoRenewSubscriptionRequestId", "SubscriptionVehicleInfoId");
                                });

                            b1.Navigation("Device");

                            b1.Navigation("LicensePlate")
                                .IsRequired();
                        });

                    b.Navigation("Bill");

                    b.Navigation("NewTrackVehicles");

                    b.Navigation("SmsBundle");

                    b.Navigation("TrackAccountSubscription");
                });

            modelBuilder.Entity("GoTrack.Requests.SmsBundleRenewalRequests.SmsBundleRenewalRequest", b =>
                {
                    b.HasOne("GoTrack.Payments.Bills.Bill", "Bill")
                        .WithMany()
                        .HasForeignKey("BillId");

                    b.HasOne("GoTrack.Requests.TrackAccountRequest", null)
                        .WithOne()
                        .HasForeignKey("GoTrack.Requests.SmsBundleRenewalRequests.SmsBundleRenewalRequest", "Id")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("GoTrack.SmsBundles.SmsBundle", "SmsBundle")
                        .WithMany()
                        .HasForeignKey("SmsBundleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("GoTrack.TrackAccounts.TrackAccountSubscriptions.TrackAccountSubscription", "TrackAccountSubscription")
                        .WithMany()
                        .HasForeignKey("TrackAccountSubscriptionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Bill");

                    b.Navigation("SmsBundle");

                    b.Navigation("TrackAccountSubscription");
                });

            modelBuilder.Entity("GoTrack.Devices.Device", b =>
                {
                    b.Navigation("StatusLogs");
                });

            modelBuilder.Entity("GoTrack.Requests.Request", b =>
                {
                    b.Navigation("Notes");
                });

            modelBuilder.Entity("GoTrack.TrackAccounts.TrackAccount", b =>
                {
                    b.Navigation("TrackAccountSubscriptions");

                    b.Navigation("UserTrackAccountAssociations");
                });

            modelBuilder.Entity("GoTrack.UserTrackAccountAssociations.UserTrackAccountAssociation", b =>
                {
                    b.Navigation("Observations");
                });

            modelBuilder.Entity("GoTrack.VehicleGroups.VehicleGroup", b =>
                {
                    b.Navigation("VehicleGroupVehicles");
                });

            modelBuilder.Entity("GoTrack.Vehicles.Vehicle", b =>
                {
                    b.Navigation("VehicleGroupVehicles");
                });

            modelBuilder.Entity("Volo.Abp.AuditLogging.AuditLog", b =>
                {
                    b.Navigation("Actions");

                    b.Navigation("EntityChanges");
                });

            modelBuilder.Entity("Volo.Abp.AuditLogging.EntityChange", b =>
                {
                    b.Navigation("PropertyChanges");
                });

            modelBuilder.Entity("Volo.Abp.Identity.IdentityRole", b =>
                {
                    b.Navigation("Claims");
                });

            modelBuilder.Entity("Volo.Abp.Identity.IdentityUser", b =>
                {
                    b.Navigation("Claims");

                    b.Navigation("Logins");

                    b.Navigation("OrganizationUnits");

                    b.Navigation("Roles");

                    b.Navigation("Tokens");
                });

            modelBuilder.Entity("Volo.Abp.Identity.OrganizationUnit", b =>
                {
                    b.Navigation("Roles");
                });

            modelBuilder.Entity("Volo.Abp.TenantManagement.Tenant", b =>
                {
                    b.Navigation("ConnectionStrings");
                });

            modelBuilder.Entity("GoTrack.AlertDefinitions.RouteAlertDefinitions.ExitingRouteAlertDefinitions.ExitingRouteAlertDefinition", b =>
                {
                    b.Navigation("RouteAlertRoutes");
                });

            modelBuilder.Entity("GoTrack.AlertDefinitions.ZoneAlertDefinitions.EnteringZoneAlertDefinitions.EnteringZoneAlertDefinition", b =>
                {
                    b.Navigation("ZoneAlertGeoZones");
                });

            modelBuilder.Entity("GoTrack.AlertDefinitions.ZoneAlertDefinitions.ExitingZoneAlertDefinitions.ExitingZoneAlertDefinition", b =>
                {
                    b.Navigation("ZoneAlertGeoZones");
                });
#pragma warning restore 612, 618
        }
    }
}
